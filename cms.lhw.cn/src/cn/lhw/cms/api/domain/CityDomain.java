package cn.lhw.cms.api.domain;

import java.io.Serializable;

public class CityDomain implements Serializable {

	private static final long serialVersionUID = -7819311498594815544L;

	//城市代码
    private String cityCode;
    
    //
    private String cityName;

    //
    private String cityNameEn;
    
    //拼音
    private String cityNamePinyin;

    //拼音首字母
    private String cityNamePinyinFirst;
    
    //国家
    private String countryCode;

    //
    private String countryName;

    //
    private String countryNameEn;
    
    // 城市封面
    private String cityCover;

	public String getCityCode() {
		return cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}

	public String getCountryCode() {
		return countryCode;
	}

	public void setCountryCode(String countryCode) {
		this.countryCode = countryCode;
	}

	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

	public String getCityNameEn() {
		return cityNameEn;
	}

	public void setCityNameEn(String cityNameEn) {
		this.cityNameEn = cityNameEn;
	}

	public String getCountryName() {
		return countryName;
	}

	public void setCountryName(String countryName) {
		this.countryName = countryName;
	}

	public String getCountryNameEn() {
		return countryNameEn;
	}

	public void setCountryNameEn(String countryNameEn) {
		this.countryNameEn = countryNameEn;
	}

	public String getCityNamePinyin() {
		return cityNamePinyin;
	}

	public void setCityNamePinyin(String cityNamePinyin) {
		this.cityNamePinyin = cityNamePinyin;
	}

	public String getCityNamePinyinFirst() {
		return cityNamePinyinFirst;
	}

	public void setCityNamePinyinFirst(String cityNamePinyinFirst) {
		this.cityNamePinyinFirst = cityNamePinyinFirst;
	}
	
	public String getCityCover() {
		return cityCover;
	}

	public void setCityCover(String cityCover) {
		this.cityCover = cityCover;
	}

	@Override
	public String toString() {
		return String
				.format("CityDomain [cityCode=%s, cityName=%s, cityNameEn=%s, cityNamePinyin=%s, cityNamePinyinFirst=%s, countryCode=%s, countryName=%s, countryNameEn=%s, cityCover=%s]",
						cityCode, cityName, cityNameEn, cityNamePinyin, cityNamePinyinFirst, countryCode, countryName,
						countryNameEn, cityCover);
	}
	
}
