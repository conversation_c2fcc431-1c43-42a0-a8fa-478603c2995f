package cn.lhw.cms.service;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.lhw.cms.dao.TransportMapper;
import cn.lhw.cms.entity.Transport;
import cn.lhw.cms.entity.TransportCriteria;

@Service
public class TransportService {
	
	@Autowired
	private TransportMapper transportMapper;
	
	public List<Transport> selectByHotelCode(String hotelCode) {
		TransportCriteria example = new TransportCriteria();
		example.createCriteria().andHotelcodeEqualTo(hotelCode);
		List<Transport> list = transportMapper.selectByExample(example);
		return list;
	}
	
	public Transport selectByHotelCodeAndId(String hotelCode, Integer id) {
		TransportCriteria example = new TransportCriteria();
		example.createCriteria().andHotelcodeEqualTo(hotelCode).andIdEqualTo(id);
		List<Transport> list = transportMapper.selectByExample(example);
		return list.get(0);
	}
	
	public boolean delTransport(Integer id) {
		return transportMapper.deleteByPrimaryKey(id) == 1 ? true : false;
	}
	
	public boolean upTransport(Transport transport) {
		Transport ts = selectByHotelCodeAndId(transport.getHotelcode(), transport.getId());
		transport.setCreatetime(ts.getCreatetime());
		transport.setUpdatetime(new Date());
		return transportMapper.updateByPrimaryKey(transport) == 1 ? true :false;
	}
	
	public boolean insertTransport(Transport transport) {
		Date date = new Date();
		transport.setCreatetime(date);
		transport.setUpdatetime(date);
		return transportMapper.insert(transport) == 1 ? true : false;
	}
}
