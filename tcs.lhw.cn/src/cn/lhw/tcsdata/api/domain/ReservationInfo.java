package cn.lhw.tcsdata.api.domain;

import java.io.Serializable;

/**
 * 订单描述信息（配合订单模板使用）
 * 
 * <AUTHOR>
 *
 */
public class ReservationInfo implements Serializable {
	
	private static final long serialVersionUID = 1L;

	private String mailTitle; //邮件标题
	
	private String hotelNameCn;//酒店名称中
	private String hotelNameEn;//酒店名称英
	
	private String confirmation; //订单号(确认号)
	private String cancellationNumber; //订单确认取消号
	
	private String mapUrl; //地图查看
	
	private String address;//酒店地址
	
	private String zipCode;//酒店邮编
	
	private String phone; //酒店电话
	
	private String inDate; //入住日期
	private String inTime; //最早入住时间
	private String outDate; //离店日期
	private String outTime; //最晚离店时间
	
	
	private String roomName;//房型名称
	private String roomNameEn;//房型名称(英文)
	private String roomDesc;//房型描述
	private String wifi;//宽带信息
	private String breakfast;//早餐信息
	private Integer adults; //成人数
	private Integer childrens; //儿童数
	private Integer rooms; //房间数
	private Integer nights; //住几晚
	
	private String bookedAt; //预订日期
	//担保规则描述
	private String guaranteeRuleText;
	//取消规则描述
	private String cancelRuleText;
	//保留时间描述
	private String holdTimeText;
	
	private String currency; //货币单位
	private Long roomRate; //房费
	private Long estimatedTotal; //服务费等杂费合计
	private Long totalRate;  //总房费
	private Long cnyTotalRate;  //人民币总房费
	//客人会员号
	private String memberNo;
	//客人电话
	private String guestPhone;
	//客人邮箱
	private String guestEmail;
	//客人姓名
	private String guestName;
	//客人称谓
	private String guestSalutation;
	
	private String guestSalutationCN;
	private String guestSalutationEN;
	
	private String remarks; //备注
	
	private String rateName; //房价名称
	private String rateDesc; //房价描述
	
	private String copyDate; //版权年限

	private String guestFirstName ;
	private String guestLastName ;
	private String guestFirstNameCN ;
	private String guestLastNameCN ;
	private String requested_service ;
	private String roomUpgrade; // 房型升级

	private String banner; //邮件banner地址
	private String url; //酒店链接地址 
	
	public String getMailTitle() {
		return mailTitle;
	}

	public void setMailTitle(String mailTitle) {
		this.mailTitle = mailTitle;
	}

	public String getHotelNameCn() {
		return hotelNameCn;
	}

	public void setHotelNameCn(String hotelNameCn) {
		this.hotelNameCn = hotelNameCn;
	}

	public String getHotelNameEn() {
		return hotelNameEn;
	}

	public void setHotelNameEn(String hotelNameEn) {
		this.hotelNameEn = hotelNameEn;
	}

	public String getConfirmation() {
		return confirmation;
	}

	public void setConfirmation(String confirmation) {
		this.confirmation = confirmation;
	}

	public String getCancellationNumber() {
		return cancellationNumber;
	}

	public void setCancellationNumber(String cancellationNumber) {
		this.cancellationNumber = cancellationNumber;
	}

	public String getMapUrl() {
		return mapUrl;
	}

	public void setMapUrl(String mapUrl) {
		this.mapUrl = mapUrl;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getZipCode() {
		return zipCode;
	}

	public void setZipCode(String zipCode) {
		this.zipCode = zipCode;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getInDate() {
		return inDate;
	}

	public void setInDate(String inDate) {
		this.inDate = inDate;
	}

	public String getInTime() {
		return inTime;
	}

	public void setInTime(String inTime) {
		this.inTime = inTime;
	}

	public String getOutDate() {
		return outDate;
	}

	public void setOutDate(String outDate) {
		this.outDate = outDate;
	}

	public String getOutTime() {
		return outTime;
	}

	public void setOutTime(String outTime) {
		this.outTime = outTime;
	}

	public String getRoomName() {
		return roomName;
	}

	public void setRoomName(String roomName) {
		this.roomName = roomName;
	}

	public String getRoomNameEn() {
		return roomNameEn;
	}

	public void setRoomNameEn(String roomNameEn) {
		this.roomNameEn = roomNameEn;
	}

	public String getRoomDesc() {
		return roomDesc;
	}

	public void setRoomDesc(String roomDesc) {
		this.roomDesc = roomDesc;
	}

	public String getWifi() {
		return wifi;
	}

	public void setWifi(String wifi) {
		this.wifi = wifi;
	}

	public String getBreakfast() {
		return breakfast;
	}

	public void setBreakfast(String breakfast) {
		this.breakfast = breakfast;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public Integer getAdults() {
		return adults;
	}

	public void setAdults(Integer adults) {
		this.adults = adults;
	}

	public Integer getChildrens() {
		return childrens;
	}

	public void setChildrens(Integer childrens) {
		this.childrens = childrens;
	}

	public Long getRoomRate() {
		return roomRate;
	}

	public void setRoomRate(Long roomRate) {
		this.roomRate = roomRate;
	}

	public Long getEstimatedTotal() {
		return estimatedTotal;
	}

	public void setEstimatedTotal(Long estimatedTotal) {
		this.estimatedTotal = estimatedTotal;
	}

	public Long getTotalRate() {
		return totalRate;
	}

	public void setTotalRate(Long totalRate) {
		this.totalRate = totalRate;
	}

	public Long getCnyTotalRate() {
		return cnyTotalRate;
	}

	public void setCnyTotalRate(Long cnyTotalRate) {
		this.cnyTotalRate = cnyTotalRate;
	}

	public String getMemberNo() {
		return memberNo;
	}

	public void setMemberNo(String memberNo) {
		this.memberNo = memberNo;
	}

	public String getGuestPhone() {
		return guestPhone;
	}

	public void setGuestPhone(String guestPhone) {
		this.guestPhone = guestPhone;
	}

	public String getGuestEmail() {
		return guestEmail;
	}

	public void setGuestEmail(String guestEmail) {
		this.guestEmail = guestEmail;
	}

	public String getGuestName() {
		return guestName;
	}

	public void setGuestName(String guestName) {
		this.guestName = guestName;
	}

	public String getGuestSalutation() {
		return guestSalutation;
	}

	public void setGuestSalutation(String guestSalutation) {
		this.guestSalutation = guestSalutation;
	}

	public String getGuestSalutationCN() {
		return guestSalutationCN;
	}

	public void setGuestSalutationCN(String guestSalutationCN) {
		this.guestSalutationCN = guestSalutationCN;
	}

	public String getGuestSalutationEN() {
		return guestSalutationEN;
	}

	public void setGuestSalutationEN(String guestSalutationEN) {
		this.guestSalutationEN = guestSalutationEN;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public String getRateName() {
		return rateName;
	}

	public void setRateName(String rateName) {
		this.rateName = rateName;
	}
	
	public String getRateDesc() {
		return rateDesc;
	}

	public void setRateDesc(String rateDesc) {
		this.rateDesc = rateDesc;
	}

	public String getCopyDate() {
		return copyDate;
	}

	public void setCopyDate(String copyDate) {
		this.copyDate = copyDate;
	}

	public String getGuaranteeRuleText() {
		return guaranteeRuleText;
	}

	public void setGuaranteeRuleText(String guaranteeRuleText) {
		this.guaranteeRuleText = guaranteeRuleText;
	}

	public String getCancelRuleText() {
		return cancelRuleText;
	}

	public void setCancelRuleText(String cancelRuleText) {
		this.cancelRuleText = cancelRuleText;
	}

	public String getHoldTimeText() {
		return holdTimeText;
	}

	public void setHoldTimeText(String holdTimeText) {
		this.holdTimeText = holdTimeText;
	}

	public Integer getRooms() {
		return rooms;
	}

	public void setRooms(Integer rooms) {
		this.rooms = rooms;
	}

	public Integer getNights() {
		return nights;
	}

	public void setNights(Integer nights) {
		this.nights = nights;
	}

	public String getBookedAt() {
		return bookedAt;
	}

	public void setBookedAt(String bookedAt) {
		this.bookedAt = bookedAt;
	}

	public String getGuestFirstName() {
		return guestFirstName;
	}

	public void setGuestFirstName(String guestFirstName) {
		this.guestFirstName = guestFirstName;
	}

	public String getGuestLastName() {
		return guestLastName;
	}

	public void setGuestLastName(String guestLastName) {
		this.guestLastName = guestLastName;
	}
	
	public String getGuestFirstNameCN() {
		if(guestFirstNameCN==null||guestFirstNameCN.equals("")){
			return getGuestFirstName();
		}
		return guestFirstNameCN;
	}

	public void setGuestFirstNameCN(String guestFirstNameCN) {
		this.guestFirstNameCN = guestFirstNameCN;
	}

	public String getGuestLastNameCN() {
		if(guestLastNameCN==null||guestLastNameCN.equals("")){
			return getGuestLastName() ;
		}
		return guestLastNameCN;
	}

	public String getRequested_service() {
		return requested_service;
	}

	public void setRequested_service(String requested_service) {
		this.requested_service = requested_service;
	}

	public void setGuestLastNameCN(String guestLastNameCN) {
		this.guestLastNameCN = guestLastNameCN;
	}

	public String getRoomUpgrade() {
		return roomUpgrade;
	}

	public void setRoomUpgrade(String roomUpgrade) {
		this.roomUpgrade = roomUpgrade;
	}

	public String getBanner() {
		return banner;
	}
	
	public void setBanner(String banner) {
		this.banner = banner;
	}
	
	public String getUrl() {
		return url;
	}
	
	public void setUrl(String url) {
		this.url = url;
	}
	
	@Override
	public String toString() {
		return String.format(
				"ReservationInfo [mailTitle=%s, hotelNameCn=%s, hotelNameEn=%s, confirmation=%s, cancellationNumber=%s, mapUrl=%s, address=%s, zipCode=%s, phone=%s, inDate=%s, inTime=%s, outDate=%s, outTime=%s, roomName=%s, roomNameEn=%s, roomDesc=%s, wifi=%s, breakfast=%s, adults=%s, childrens=%s, rooms=%s, nights=%s, bookedAt=%s, guaranteeRuleText=%s, cancelRuleText=%s, holdTimeText=%s, currency=%s, roomRate=%s, estimatedTotal=%s, totalRate=%s, cnyTotalRate=%s, memberNo=%s, guestPhone=%s, guestEmail=%s, guestName=%s, guestSalutation=%s, guestSalutationCN=%s, guestSalutationEN=%s, remarks=%s, rateName=%s, rateDesc=%s, copyDate=%s, guestFirstName=%s, guestLastName=%s, guestFirstNameCN=%s, guestLastNameCN=%s, requested_service=%s, roomUpgrade=%s, banner=%s, url=%s]",
				mailTitle, hotelNameCn, hotelNameEn, confirmation, cancellationNumber, mapUrl, address, zipCode, phone,
				inDate, inTime, outDate, outTime, roomName, roomNameEn, roomDesc, wifi, breakfast, adults, childrens,
				rooms, nights, bookedAt, guaranteeRuleText, cancelRuleText, holdTimeText, currency, roomRate,
				estimatedTotal, totalRate, cnyTotalRate, memberNo, guestPhone, guestEmail, guestName, guestSalutation,
				guestSalutationCN, guestSalutationEN, remarks, rateName, rateDesc, copyDate, guestFirstName,
				guestLastName, guestFirstNameCN, guestLastNameCN, requested_service, roomUpgrade, banner, url);
	}
	
}
