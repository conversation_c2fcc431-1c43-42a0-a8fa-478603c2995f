package cn.lhw.weixin.utils;

import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import cn.lhw.member.config.WeiXinConfig;
import cn.lhw.member.controller.LhwController;

import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

public class MessageHandlerUtil {

    static final int UP_SCORE = (int) (5000 * 12.5);

    /**
     * 解析微信发来的请求（XML）
     *
     * @param request
     * @return map
     * @throws Exception
     */
    public static Map<String, String> parseXml(HttpServletRequest request) throws Exception {
        // 将解析结果存储在HashMap中
        Map<String, String> map = new HashMap<String, String>();
        // 从request中取得输入流
        InputStream inputStream = request.getInputStream();
        System.out.println("获取输入流");
        // 读取输入流
        SAXReader reader = new SAXReader();
        Document document = reader.read(inputStream);
        // 得到xml根元素
        Element root = document.getRootElement();
        // 得到根元素的所有子节点
        List<Element> elementList = root.elements();

        // 遍历所有子节点
        for (Element e : elementList) {
            System.out.println(e.getName() + "|" + e.getText());
            map.put(e.getName(), e.getText());
        }

        // 释放资源
        inputStream.close();
        inputStream = null;
        return map;
    }

    // 根据消息类型 构造返回消息
    public static String buildXml(Map<String, String> map, Map<String, String> map2) {
        String result;
        String fromUserName = map.get("FromUserName");
        // 开发者微信号
        String toUserName = map.get("ToUserName");
        String message = "";
        String level = map2.get("level");
        String upgrade = map2.get("upgrade");
        String pointsBankExpirationDate = map2.get("pointsBankExpirationDate");
        String lcnumber = map2.get("lcnumber");
        if (null != level && !"".equals(level)) {
            String score = map2.get("point");
            String allConsume = map2.get("allConsume");
            String expiredDate = map2.get("expiredDate");
            //int allConsumeUsd = (int) (Integer.parseInt(allConsume)/12.5);
            int needConsumeUsd = (int) ((int) (UP_SCORE - Integer.parseInt(allConsume)) / 12.5);
            float allConsumeUsd = Float.parseFloat(map2.get("memberTransaction"));
//        	String url = WeiXinConfig.weixinmessageurl+"/lhw-member/member-rights";
            if (level.equalsIgnoreCase("贵宾会员")) {
                message = "我的会员号：" + lcnumber + "\n我的会员级别：贵宾会员\n我的积分：" + score + "\n我的积分有效期：" + pointsBankExpirationDate + "\n可用入住前客房升级次数：" + upgrade + "\n本年度累计有效消费：$" + allConsumeUsd + "\n本年度累计有效消费达$5,000可解锁银樽会员权益\n <a href='https://www.lhw.cn/terms-and-conditions#leadersclub'>**尊享贵宾会条款适用</a> \n查看<a href='http://m.lhw.cn/leaders-club/benefits'> 我的会员权益 </a>";
//        		message = "我的积分："+score+"\n我的等级：贵宾会员\n可用房型升级次数："+upgrade+"\n我的累计消费：$"+allConsumeUsd+"\n距下一等级还需消费：$"+needConsumeUsd+"\n查看<a href='"+url+"'> 我的会员权益 </a>";
            } else if (level.equalsIgnoreCase("银樽会员")) {
                message = "我的会员号：" + lcnumber + "\n我的会员级别：银樽会员\n我的积分：" + score + "\n我的积分有效期：" + pointsBankExpirationDate + "\n可用入住前客房升级次数：" + upgrade + "\n本年度累计有效消费：$" + allConsumeUsd + "\n本年度累计有效消费达$5,000可保持银樽会员权益 \n <a href='https://www.lhw.cn/terms-and-conditions#leadersclub'>**尊享贵宾会条款适用</a>\n查看<a href='http://m.lhw.cn/leaders-club/benefits-sterling'> 我的会员权益 </a>";
            } else if (level.equalsIgnoreCase("金鼎会员")) {
                message = "我的会员号：" + lcnumber + "\n我的会员级别：金鼎会员\n我的积分：" + score + "\n我的积分有效期：" + pointsBankExpirationDate + "\n本年度累计有效消费：$" + allConsumeUsd + "\n查看<a href='http://m.lhw.cn/leaders-club/benefits-aurelian'> 我的会员权益 </a>";
            }

            if (null != expiredDate && !"".equals(expiredDate)) {
                message = message + "\n抱歉，您的会籍已于" + expiredDate + "到期，请及时拨打400-1324-582进行续费，以享受各项会员权益";
            }
            if (null != map2.get("Cancelled")) {
                message = message + "\n抱歉，您的会籍已被取消，如有疑问请拨打400-1324-582";
            }


        } else {
            message = "没有查询到您的相关信息！";
        }
        result = String
                .format(
                        "<xml>" +
                                "<ToUserName><![CDATA[%s]]></ToUserName>" +
                                "<FromUserName><![CDATA[%s]]></FromUserName>" +
                                "<CreateTime>%s</CreateTime>" +
                                "<MsgType><![CDATA[text]]></MsgType>" +
                                "<Content><![CDATA[%s]]></Content>" +
                                "</xml>",
                        fromUserName, toUserName, getUtcTime(), message);

        return result;
    }

    public static String buildNotLoginXml(Map<String, String> map) {
        String result;
        String fromUserName = map.get("FromUserName");
        // 开发者微信号
        String toUserName = map.get("ToUserName");
        String url = WeiXinConfig.weixinmessageurl + "/lhw-member/login/" + fromUserName;
        String url2 = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" + WeiXinConfig.appId +
                "&redirect_uri=" + WeiXinConfig.weixinmessageurl + "/lhw-member/basic-info&response_type=code&scope=snsapi_base&state=1#wechat_redirect";
        result = String.format(
                "<xml>" +
                        "<ToUserName><![CDATA[%s]]></ToUserName>" +
                        "<FromUserName><![CDATA[%s]]></FromUserName>" +
                        "<CreateTime>%s</CreateTime>" +
                        "<MsgType><![CDATA[text]]></MsgType>" +
                        "<Content><![CDATA[%s]]></Content>" +
                        "</xml>",
                fromUserName, toUserName, getUtcTime(),
                "如果您已是尊享贵宾会会员，请点击<a href='" + url + "'> 会员登录 </a>进行绑定, 以便在微信平台进行更多会员操作。\n"
                        + "如果您还未成为尊享贵宾会会员，请查看<a href='" + url2 + "'> 会员权益</a>。");

        return result;
    }

    /**
     * 构造文本消息
     *
     * @param map
     * @param content
     * @return
     */
    public static String buildTextMessage(Map<String, String> map, String content) {
        //发送方帐号
        String fromUserName = map.get("FromUserName");
        // 开发者微信号
        String toUserName = map.get("ToUserName");
        /**
         * 文本消息XML数据格式
         * <xml>
         <ToUserName><![CDATA[toUser]]></ToUserName>
         <FromUserName><![CDATA[fromUser]]></FromUserName>
         <CreateTime>1348831860</CreateTime>
         <MsgType><![CDATA[text]]></MsgType>
         <Content><![CDATA[this is a test]]></Content>
         <MsgId>1234567890123456</MsgId>
         </xml>
         */
        return String.format(
                "<xml>" +
                        "<ToUserName><![CDATA[%s]]></ToUserName>" +
                        "<FromUserName><![CDATA[%s]]></FromUserName>" +
                        "<CreateTime>%s</CreateTime>" +
                        "<MsgType><![CDATA[text]]></MsgType>" +
                        "<Content><![CDATA[%s]]></Content>" + "</xml>",
                fromUserName, toUserName, getUtcTime(), content);
    }

    /**
     * 构造文本消息
     *
     * @param map
     * @param content
     * @return
     */
    public static String buildSuccessMessage(String openid, String content) {
        System.out.println("发送登陆成功消息");
        //发送方帐号
        String fromUserName = LhwController.fromUser;
        // 开发者微信号
        String toUserName = openid;
        /**
         * 文本消息XML数据格式
         * <xml>
         <ToUserName><![CDATA[toUser]]></ToUserName>
         <FromUserName><![CDATA[fromUser]]></FromUserName>
         <CreateTime>1348831860</CreateTime>
         <MsgType><![CDATA[text]]></MsgType>
         <Content><![CDATA[this is a test]]></Content>
         <MsgId>1234567890123456</MsgId>
         </xml>
         */
        return String.format(
                "<xml>" +
                        "<ToUserName><![CDATA[%s]]></ToUserName>" +
                        "<FromUserName><![CDATA[%s]]></FromUserName>" +
                        "<CreateTime>%s</CreateTime>" +
                        "<MsgType><![CDATA[text]]></MsgType>" +
                        "<Content><![CDATA[%s]]></Content>" + "</xml>",
                fromUserName, toUserName, getUtcTime(), content);
    }

    private static String getUtcTime() {
        Date dt = new Date();// 如果不需要格式,可直接用dt,dt就是当前系统时间
        DateFormat df = new SimpleDateFormat("yyyyMMddhhmm");// 设置显示格式
        String nowTime = df.format(dt);
        long dd = (long) 0;
        try {
            dd = df.parse(nowTime).getTime();
        } catch (Exception e) {

        }
        return String.valueOf(dd);
    }

    public static String buildAutoReplyMessage(Map<String, String> map, String index) {
        String result;
        String fromUserName = map.get("FromUserName");
        // 开发者微信号
        String toUserName = map.get("ToUserName");
        String title;
        String description;
        String picurl;
        String url;
        switch (index) {
            case "1":
                title = "立鼎世品牌故事";
                description = "400余家独立传承且别具一格的奢华成员酒店分布于全球80多个国家。";
                picurl = "https://mmbiz.qpic.cn/mmbiz_png/BW3qCmyYV36nib3OFtDnT1GrBnNRTETOomTEXj89skUX7aiayyq7OzibpkoSdfEGm4FiaMqib5KxgPS9Sib5W9c2IzbQ/640?wx_fmt=png&amp;from=appmsg";
                url = "https://mp.weixin.qq.com/s/5iEP_qSjrKW-JtKNU5O1uA";
                break;
            case "2":
                title = "立鼎世甄选酒店";
                description = "成员酒店都独具特色，为宾客缔造难忘体验，讲述属于自己的故事。";
                picurl = "https://mmbiz.qpic.cn/mmbiz_png/BW3qCmyYV36nib3OFtDnT1GrBnNRTETOofecR6f62ZBJVVYibHIJnvf2ft2ervYGSWMAOgcskGibo4fEericDRWIeg/640?wx_fmt=png&amp;from=appmsg";
                url = "https://mp.weixin.qq.com/mp/homepage?__biz=MjM5MTAxNTE0Nw==&hid=2&sn=c447b100b42baa631cb15ac501fd025a&scene=1&devicetype=Windows+10+x64&version=63090819&lang=zh_CN&nettype=WIFI&ascene=0&session_us=gh_0f0726b59207&uin=&key=";
                break;
            case "3":
                title = "立鼎世荣誉";
                description = "成员酒店为宾客缔造难忘体验，在众多的权威评选中屡获殊荣。";
                picurl = "https://mmbiz.qpic.cn/mmbiz_png/BW3qCmyYV36nib3OFtDnT1GrBnNRTETOoibjGAgic1O9Gz7jcEbaIpmxAR6det79jklVL8nNeyFa4vmLcicloB3NjA/640?wx_fmt=png&from=appmsg";
                url = "https://mp.weixin.qq.com/mp/homepage?__biz=MjM5MTAxNTE0Nw==&hid=3&sn=280b117753c106791b60868fb82efdb7&scene=1&devicetype=Windows+10+x64&version=63090819&lang=zh_CN&nettype=WIFI&ascene=0&session_us=gh_0f0726b59207&uin=&key=";
                break;
            case "4":
                title = "尊享贵宾会";
                description = "免费忠诚宾客计划为会员提供个性化服务和众多专享旅行优惠。";
                picurl = "https://mmbiz.qpic.cn/mmbiz_png/BW3qCmyYV36nib3OFtDnT1GrBnNRTETOoxExibBPvAp9jKDY7RjiaV5M8WM5B4XqHQIiaE5oQBFLegeeHP2zX9cayw/640?wx_fmt=png&from=appmsg";
                url = "https://mp.weixin.qq.com/s/geRO6fsEDqmdgqwD1yNV6A";
                break;
            default:
                title = "点击注册会籍";
                description = "点击注册免费会籍，享丰富会员权益及福利。";
                picurl = "https://mmbiz.qpic.cn/mmbiz_png/BW3qCmyYV36nib3OFtDnT1GrBnNRTETOoPKuw41zxHfHtAsVT13rmgNj9InZficPteV02twUrbdFeZcibZnVAJncQ/640?wx_fmt=png&amp;from=appmsg";
                url = "https://m.lhw.cn/register.html?utm_medium=social_media&utm_source=wechat&utm_campaign=Welcom_Messapge&utm_content=Welcome_Message";
                break;
        }

        result = String.format(
                "<xml>" +
                        "<ToUserName><![CDATA[%s]]></ToUserName>" +
                        "<FromUserName><![CDATA[%s]]></FromUserName>" +
                        "<CreateTime>%s</CreateTime>" +
                        "<MsgType><![CDATA[news]]></MsgType>" +
                        "<ArticleCount>1</ArticleCount>" +
                        "<Articles>" +
                        "<item>" +
                        "<Title><![CDATA[%s]]></Title>" +
                        "<Description><![CDATA[%s]]></Description>" +
                        "<PicUrl><![CDATA[%s]]></PicUrl>" +
                        "<Url><![CDATA[%s]]></Url>" +
                        "</item>" +
                        "</Articles>" +
                        "</xml>",
                fromUserName, toUserName, getUtcTime(), title, description, picurl, url);
        return result;
    }
}
