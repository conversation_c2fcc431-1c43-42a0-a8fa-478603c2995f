package cn.lhw.member.controller;


import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.lhw.member.xml.transaction.FetchMemberTransactionsReturn;
import cn.lhw.member.xml.transaction.MemberTransactionItem;
import cn.lhw.member.xml.transaction.MemberTransactions;
import com.alibaba.druid.sql.parser.Lexer;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.itcrowd.framework.util.MethodResult;

import cn.lhw.member.config.WeiXinConfig;
import cn.lhw.member.redis.RedisService;
import cn.lhw.member.service.AimiaService;
import cn.lhw.member.service.MemberService;
import cn.lhw.member.xml.aimia.user.Profile;
import cn.lhw.member.xml.points.FetchMemberPointBalancesReturn;
import cn.lhw.member.xml.rewards.MemberActiveRewardWithRewardCustomFieldsItem;
import cn.lhw.member.xml.rewards.MemberActiveRewards;
import cn.lhw.member.xml.rewards.MemberActiveRewardsWithRewardCustomFieldsReturn;
import cn.lhw.weixin.utils.MessageHandlerUtil;
import cn.lhw.weixin.utils.MessageType;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/lhw")
public class LhwController {

    @Autowired
    RedisService redisService;
    @Autowired
    MemberService memberService;
    @Autowired
    AimiaService aimiaService;
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * Token可由开发者可以任意填写，用作生成签名（该Token会和接口URL中包含的Token进行比对，从而验证安全性）
     * 比如这里我将Token设置为gacl
     */
//	private final String TOKEN = "lhw_member";

    public static final String fromUser = "gh_d6b1295ff907";

    /**
     * @return
     */
    @RequestMapping(value = "/wlecome", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public MethodResult getWelcomeMessage() {
        MethodResult reslute = new MethodResult();
        reslute.setSuccess(true);
        reslute.setData("您好，感谢关注立鼎世集团酒店！");
        return reslute;
    }


    /**
     * @return
     */
    @RequestMapping(value = "/token", method = RequestMethod.GET)
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        System.out.println("开始校验签名");
        /**
         * 接收微信服务器发送请求时传递过来的4个参数
         */
        String signature = request.getParameter("signature");//微信加密签名signature结合了开发者填写的token参数和请求中的timestamp参数、nonce参数。
        String timestamp = request.getParameter("timestamp");//时间戳
        String nonce = request.getParameter("nonce");//随机数
        String echostr = request.getParameter("echostr");//随机字符串
        //排序
        String sortString = sort(WeiXinConfig.TOKEN, timestamp, nonce);
        //加密
        String mySignature = sha1(sortString);
        //校验签名
        if (mySignature != null && mySignature != "" && mySignature.equals(signature)) {
            System.out.println("签名校验通过。");
            //如果检验成功输出echostr，微信服务器接收到此输出，才会确认检验完成。
            //response.getWriter().println(echostr);
            response.getWriter().write(echostr);
        } else {
            System.out.println("签名校验失败.");
        }

    }

    /**
     * 处理微信服务器发来的消息
     */
    /**
     * @return
     */
    @RequestMapping(value = "/token", method = RequestMethod.POST)
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        // TODO 接收、处理、响应由微信服务器转发的用户发送给公众帐号的消息
        // 将请求、响应的编码均设置为UTF-8（防止中文乱码）
        request.setCharacterEncoding("UTF-8");
        response.setCharacterEncoding("UTF-8");
        System.out.println("请求进入");
        String result = "";
        String defaultMessage = "Hi, 您好，感谢关注立鼎世酒店集团，请随我们一起体验“卓尔不凡，博览臻萃”。\r\n" + "\r\n";
        defaultMessage += "现在免费加入尊享贵宾会，入住全球400余家卓尔不凡的成员酒店时均可享免费早餐、欢迎礼遇、会员专享优惠高至最优房价8折，" +
                "还可累积积分兑换免费入住，使旅程更精彩。\r\n"
                    +"\r\n"
                    +"点击<a href=\"http://m.lhw.cn/leaders-club/benefits?utm_medium=social_media&utm_source=wechat&utm_campaign=Welcom_Messapge&utm_content=Welcome_Message\">此处</a>了解会员权益并注册，也可点击公众号菜单“尊享贵宾会-专享优惠”了解会员权益并进行更多操作。";
        try {
            Map<String, String> map = MessageHandlerUtil.parseXml(request);
            String fromUserName = map.get("FromUserName");
            System.out.println("fromUserName>>>>>>" + fromUserName);
            System.out.println("开始构造消息");

            String msgType = map.get("MsgType").toString();
            System.out.println("MsgType:" + msgType);
            //其他消息flag
            String otherMessageKey = "om:" + fromUser;
            if (msgType.toUpperCase().equals(MessageType.REQ_MESSAGE_TYPE_EVENT)
                    && map.get("Event").equalsIgnoreCase(MessageType.EVENT_TYPE_CLICK)
                    && map.get("EventKey").equalsIgnoreCase("lhw_rank")) {
//		    	boolean login = false;
                String uuid = redisService.get("openid" + fromUserName, String.class);
                if (null != uuid && !"".equals(uuid)) {
//	    			FetchMemberPointBalancesReturn points = memberService.getMemberPoint(uuid);
//	    			Profile profile = aimiaService.getProfile(uuid);
//	    			FetchMemberHistoryReturn histroy = memberService.getHistory(uuid);

                    ///////////////////////优化的代码
                    CompletableFuture<FetchMemberPointBalancesReturn> pointsFeture = CompletableFuture.supplyAsync(() -> {
                        return memberService.getMemberPoint(uuid);
                    });
                    CompletableFuture<Profile> profileFeture = CompletableFuture.supplyAsync(() -> {
                        return aimiaService.getProfile(uuid);
                    });
//	    			CompletableFuture<FetchMemberHistoryReturn> histroyFeture  = CompletableFuture.supplyAsync(()->{
//						return memberService.getHistory(uuid);
//					});
                    CompletableFuture<MemberActiveRewardsWithRewardCustomFieldsReturn> rewardsFeture = CompletableFuture.supplyAsync(() -> {
                        return memberService.getRewards(uuid);
                    });
                    CompletableFuture<FetchMemberTransactionsReturn> memberTransactionTotalFeture = CompletableFuture.supplyAsync(() -> {
                        return memberService.getTransaction(uuid);
                    });
                    CompletableFuture<Void> future = CompletableFuture.allOf(pointsFeture, profileFeture, rewardsFeture, memberTransactionTotalFeture);
                    try {
                        future.get(160, TimeUnit.SECONDS);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    } catch (ExecutionException e) {
                        e.printStackTrace();
                    } catch (java.util.concurrent.TimeoutException e) {
                        e.printStackTrace();
                    }
                    FetchMemberPointBalancesReturn points = pointsFeture.get();
                    Profile profile = profileFeture.get();
//	    			FetchMemberHistoryReturn histroy = histroyFeture.get();
                    MemberActiveRewardsWithRewardCustomFieldsReturn rewards = rewardsFeture.get();

                    FetchMemberTransactionsReturn transactionsReturn = memberTransactionTotalFeture.get();
                    MemberTransactions item = transactionsReturn.getMemberTransactions();
                    Calendar calendar = Calendar.getInstance();

                    String startTime = calendar.get(Calendar.YEAR) + "-01-01 00:00:00.000";
                    //String startTime = "2018-08-15 00:00:00.000";
                    //输出交易金额
                    float memberTransactionTotal = 0.00F;
                    if (item != null) {
                        for (MemberTransactionItem memberTransaction : item.getMemberTransactionItem()) {
                            if (startTime.compareTo(memberTransaction.getTransactionTimestampUTC()) < 0 && memberTransaction.getTransactionIsCancelled().intValue() == 0) {
                                memberTransactionTotal += memberTransaction.getTransactionRetailValue();
                            }
                        }
                    }
                    ///////////////////////优化的代码结束

                    Map<String, String> map2 = new HashMap<String, String>();
                    if (null != points.getReturnCode() && points.getReturnCode().intValue() == 0) {
                        map2.put("point", points.getMemberPointsAvailable().toString());
                        map2.put("allConsume", points.getMemberPointsEarned().toString());
                        map2.put("memberTransaction", String.valueOf(memberTransactionTotal));
                    } else {
                        map2.put("point", "0");
                        map2.put("allConsume", "0");
                        map2.put("memberTransaction", "0.00");
                    }
                    if (null != profile) {
                        String level = profile.getMembershiptier();
                        if (StringUtils.isNotBlank(level)) {
                            map2.put("lcnumber", profile.getMembershipnumber());
                            if (level.equalsIgnoreCase("Club")) {
                                map2.put("level", "贵宾会员");
                            }
                            if (level.equalsIgnoreCase("Sterling")) {
                                map2.put("level", "银樽会员");
                            }
                            if (level.equalsIgnoreCase("Aurelian")) {
                                map2.put("level", "金鼎会员");
                            }
                        }
//	    				profile.setStatus("Cancelled");
                        String isExpired = profile.getStatus();
                        if (StringUtils.isNotBlank(isExpired)) {
                            if ("Expired".equals(isExpired) || "Suspended".equals(isExpired)) {
                                map2.put("expiredDate", profile.getExpirationdate());
                            }
                            if ("Cancelled".equals(isExpired)) {
                                map2.put("Cancelled", "Cancelled");
                            }
                        }
                        map2.put("pointsBankExpirationDate", profile.getPointsBankExpirationDate());
                    }
                    map2.put("upgrade", "0");
                    System.out.println(map2);
                    logger.info("map2：data->", map2);

                    if (null != rewards && null != rewards.getReturnCode() && rewards.getReturnCode().intValue() == 0) {
                        MemberActiveRewards memberActiveRewards = rewards.getMemberActiveRewards();
                        if (null != memberActiveRewards && null != memberActiveRewards.getMemberActiveRewardWithRewardCustomFieldsItem()) {
                            List<MemberActiveRewardWithRewardCustomFieldsItem> memberActiveRewardWithRewardCustomFieldsItem
                                    = memberActiveRewards.getMemberActiveRewardWithRewardCustomFieldsItem();
                            map2.put("upgrade", memberActiveRewardWithRewardCustomFieldsItem.size() + "");
                        }
                    }
//	    			if(null!=histroy&&null!=histroy.getReturnCode()&&histroy.getReturnCode().intValue()==0) {
//	    				MemberIssuedRewards upgrades = histroy.getMemberIssuedRewards();
//	    				if(null!=upgrades) {
//	    					List<MemberIssuedRewardItem> list = upgrades.getMemberIssuedRewardItem();
//	    					if(null!=list&&list.size()>0) {
//	    						for(MemberIssuedRewardItem item:list) {
//	    							String rewardTypeExternalReference = item.getRewardTypeExternalReference();
//	    							String[] rewards = rewardTypeExternalReference.split("_");
//	    							if(null!=rewards&&rewards.length>=2&&rewards[0].equalsIgnoreCase("Upgrade")) {
//	    								map2.put("upgrade",rewards[1] );
//	    							}
//	    						}
//	    					}
//	    				}
//	    			}
                    result = MessageHandlerUtil.buildXml(map, map2);
                } else {
                    result = MessageHandlerUtil.buildNotLoginXml(map);
                }
            } else if (msgType.toUpperCase().equals(MessageType.REQ_MESSAGE_TYPE_TEXT)) {
                String messageValue = map.get("Content").trim();
                switch (messageValue) {
                    case "1":
                        result = MessageHandlerUtil.buildAutoReplyMessage(map, "1");
                        break;
                    case "2":
                        result = MessageHandlerUtil.buildAutoReplyMessage(map, "2");
                        break;
                    case "3":
                        result = MessageHandlerUtil.buildAutoReplyMessage(map, "3");
                        break;
                    case "4":
                        result = MessageHandlerUtil.buildAutoReplyMessage(map, "4");
                        break;
                    case "5":
                        result = MessageHandlerUtil.buildAutoReplyMessage(map, "5");
                        break;
                    default:
                        //如果发送了此消息则24小时不在发送该消息
                        if (redisService.get(otherMessageKey, String.class) != null){
                            System.out.println("send message");
                        }else {
                            result = MessageHandlerUtil.buildTextMessage(map, "您好，感谢关注立鼎世酒店集团，回复以下数字了解更多详情：\r\n" +
                                    "【1】关于立鼎世\r\n" +
                                    "【2】立鼎世臻选\r\n" +
                                    "【3】立鼎世荣誉\r\n" +
                                    "【4】尊享贵宾会权益\r\n" +
                                    "【5】免费注册会籍\r\n" +
                                    "\r\n" +
                                    "\r\n" +
                                    "博览臻萃，卓尔不凡，立鼎世酒店集团将带您领略非凡体验，欢迎登录官网www.lhw.cn或拨打客服热线:400-1324-582 或，我们将为您提供优质服务。");
                            redisService.addWithHours(otherMessageKey, "1", 24);
                        }
                        break;
                }

            } else if (msgType.toUpperCase().equals(MessageType.REQ_MESSAGE_TYPE_EVENT)
                    && map.get("Event").equalsIgnoreCase(MessageType.EVENT_TYPE_SUBSCRIBE)) {
                result = MessageHandlerUtil.buildTextMessage(map, defaultMessage);
            }else if (msgType.toUpperCase().equals(MessageType.REQ_MESSAGE_TYPE_EVENT)
                    && map.get("Event").equalsIgnoreCase(MessageType.EVENT_TYPE_UNSUBSCRIBE)){
                    //删除标记
                    redisService.delete(otherMessageKey);
            }else {
                result = MessageHandlerUtil.buildTextMessage(map, defaultMessage);
            }

//    		MemberService memberService = new MemberServiceImpl();
//    		FetchMemberPointBalancesReturn point = memberService.getMemberPoint("12345");
            System.out.println(result);
            if (result.equals("")) {
                result = "未正确响应";
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("发生异常：" + e.getMessage());
        }
        response.getWriter().println(result);
    }

    /**
     * 排序方法
     *
     * @param token
     * @param timestamp
     * @param nonce
     * @return
     */
    public String sort(String token, String timestamp, String nonce) {
        String[] strArray = {token, timestamp, nonce};
        Arrays.sort(strArray);
        StringBuilder sb = new StringBuilder();
        for (String str : strArray) {
            sb.append(str);
        }

        return sb.toString();
    }

    /**
     * 将字符串进行sha1加密
     *
     * @param str 需要加密的字符串
     * @return 加密后的内容
     */
    public String sha1(String str) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-1");
            digest.update(str.getBytes());
            byte messageDigest[] = digest.digest();
            // Create Hex String
            StringBuffer hexString = new StringBuffer();
            // 字节数组转换为 十六进制 数
            for (int i = 0; i < messageDigest.length; i++) {
                String shaHex = Integer.toHexString(messageDigest[i] & 0xFF);
                if (shaHex.length() < 2) {
                    hexString.append(0);
                }
                hexString.append(shaHex);
            }
            return hexString.toString();

        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return "";
    }
}
