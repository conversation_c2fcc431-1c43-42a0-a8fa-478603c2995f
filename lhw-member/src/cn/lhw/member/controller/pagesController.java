package cn.lhw.member.controller;

import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import cn.lhw.member.config.MemberConfig;
import cn.lhw.member.service.*;
import cn.lhw.member.utils.HttpJson;
import cn.lhw.member.xml.aimia.user.*;
import com.itcrowd.framework.util.GsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestBody;

import com.auth0.json.auth.UserInfo;
import com.google.gson.Gson;

import cn.lhw.member.entity.BasicInfo;
import cn.lhw.member.entity.Login;
import cn.lhw.member.entity.MemberActivity;
import cn.lhw.member.entity.MemberInfoResult;
import cn.lhw.member.entity.MemberState;
import cn.lhw.member.entity.ResvBase;
import cn.lhw.member.redis.RedisService;
import cn.lhw.member.utils.PinyinUtils;
import cn.lhw.weixin.utils.MessageHandlerUtil;


/**
 * 
 * <AUTHOR>
 *
 */
@Controller
public class pagesController{
	
	private Logger logger = LoggerFactory.getLogger(this.getClass());
	
	static final String SUCCESS = "SUCCESS";
	static final String FAIL = "FAIL";
	@Autowired
	LoginService loginService;
	@Autowired
	LoginLogService loginLogService;
	@Autowired
	RedisService redisService;
	@Autowired
	BasicInfoService basicInfoService;
	@Autowired
	MemberMessageService memberMessageService;
	@Autowired
	DbResvervationService dbResvervationService;
	@Autowired
	IdmAuthService idmAuthService;
	@Autowired
	MemberStateService memberStateService;
	@Autowired
	AimiaService aimiaService;
	@Autowired
	MemberService memberService;
	/**
	 * @return
	 */
	@RequestMapping(value="/basic-info", method=RequestMethod.GET)
	public String getBasicInfo(HttpServletRequest request,String code,Model model,String lguuid) {
		logger.info("basic-info获取到的code为："+code);
		if(null==code&&null==lguuid) {
			return "errorMessage";
		}
		  
		if(null!=lguuid&&!"".equals(lguuid)) {
			String uuid = redisService.get("openid"+lguuid, String.class);
			BasicInfo info = null;
			Gson gson = new Gson();
			String beforeString = redisService.get("basic_Info_"+uuid, String.class);
			if(null!=beforeString&&!"".equals(beforeString)) {
				info = gson.fromJson(beforeString, BasicInfo.class);
			}else {
				info = basicInfoService.getBasicInfo(uuid);
			}
			model.addAttribute("info", info);
			return "basic-info";
		}else {
			String openid = loginService.getOpenId(code);
			model.addAttribute("openid", openid);
			model.addAttribute("beforepage", "basic-info");
			
			String uuid = redisService.get("openid"+openid, String.class);
			if(null!=uuid&&!"".equals(uuid)) {
				BasicInfo info = null;
				Gson gson = new Gson();
				String beforeString = redisService.get("basic_Info_"+uuid, String.class);
				if(null!=beforeString&&!"".equals(beforeString)) {
					info = gson.fromJson(beforeString, BasicInfo.class);
				}else {
					info = basicInfoService.getBasicInfo(uuid);
				}
				model.addAttribute("info", info);
				return "basic-info";
			}else {
				//跳转到链接https://m.lhw.cn/leaders-club/benefits
				return "redirect:https://m.lhw.cn/leaders-club/benefits";
			}
		}
	}

	@RequestMapping(value="/web/login", method=RequestMethod.GET)
	public String webLogin(HttpServletRequest request,String code,Model model) {
		logger.info("web/login获取到的code为："+code);
		  
		return code;
	}
	
	@RequestMapping(value="/login", method=RequestMethod.GET)
	public String login(Model model,String page,String openid) {
		logger.info("login获取到的openid为："+openid);
		model.addAttribute("openid", openid);
		model.addAttribute("beforepage", page);
		return "login-form";
	}
	
	@RequestMapping(value="/login/{openid}", method=RequestMethod.GET)
	public String login2(@PathVariable String openid,Model model) {
		logger.info("/login/【openid】获取到的openid为："+openid);
		model.addAttribute("openid", openid);
		model.addAttribute("beforepage", null);
		MessageHandlerUtil.buildSuccessMessage(openid, "登陆成功");
		return "login-form";
	}
	
	@RequestMapping(value="/forward", method=RequestMethod.GET)
	public String forward(String openid,String beforepage,Model model) {
		System.out.println("/forward"+openid);
		System.out.println("/forward"+beforepage);
		model.addAttribute("openid", openid);
		model.addAttribute("beforepage", beforepage);
		return "login";
	}
	
	@RequestMapping(value="/member-rights", method=RequestMethod.GET)
	public String memberRights() {
		return "member-rights";
	}
	
	@ResponseBody
	@RequestMapping(value="/login-in", method=RequestMethod.POST)
	public ResponseEntity<Login> loginIn(String openid,String page,String email,String pwd) {
		logger.info("/login-in获取到的email为："+email);
//		email = "<EMAIL>";
//		pwd = "welcome2Auth0!";
		Login loginDtD = new Login();
		String uuid = loginService.loginIn(email, pwd);
		if(null!=uuid) {
			uuid = uuid.trim();
			String logined = redisService.get("uuid_"+uuid, String.class);
			if(null!=logined&&!"".equals(logined)) {
				redisService.delete("openid"+logined);
				redisService.delete("uuid_"+uuid);
				redisService.delete("uuidforemail"+uuid);
			}
			redisService.addForever("openid"+openid, uuid);
			redisService.addForever("uuid_"+uuid, openid);
			redisService.addForever("uuidforemail"+uuid, email);
			try {
				loginLogService.add(uuid,email,openid);
			} catch (Exception e) {
				logger.error("添加登录log日志异常" + email );
			}
			loginDtD.setSuccess("true");
			loginDtD.setUuid(uuid);
		}else {
			loginDtD.setSuccess("false");
			logger.error("/login-in登录失败："+email+"***"+pwd);
		}
		return new ResponseEntity<Login>(loginDtD, HttpStatus.OK);
	}
	
	@RequestMapping(value="/helps", method=RequestMethod.GET)
	public String getHelps(Model model) {
		model.addAttribute("openid", "12345");
		model.addAttribute("beforepage", "basic-info");
		return "login";
	}
	
	@RequestMapping(value="/member-message", method=RequestMethod.GET)
	public String getMemberMessage(HttpServletRequest request,String code,Model model,String lguuid) {
		logger.info("/member-message获取到的code为："+code);
		if(null==code&&null==lguuid) {
			return "errorMessage";
		}
		
		if(null!=lguuid&&!"".equals(lguuid)) {
			String uuid = redisService.get("openid"+lguuid, String.class);
			List<MemberActivity> activityList = memberMessageService.getMemberMessage(uuid);
			model.addAttribute("uuid", uuid);
			model.addAttribute("activityList", activityList);
			return "member-message";
		}else {
			String openid = loginService.getOpenId(code);
			model.addAttribute("openid", openid);
			model.addAttribute("beforepage", "member-message");
			
			String uuid = redisService.get("openid"+openid, String.class);
			model.addAttribute("uuid", uuid);
			if(null!=uuid&&!"".equals(uuid)) {
				List<MemberActivity> activityList = memberMessageService.getMemberMessage(uuid);
				model.addAttribute("activityList", activityList);
				return "member-message";
			}else {
				return "redirect:https://m.lhw.cn/leaders-club/benefits";
			}
		}
	}
	
	@ResponseBody
	@RequestMapping(value="/activity-in", method=RequestMethod.POST)
	public ResponseEntity<String> activityIn(HttpServletRequest request,String actId,String uuid) {
		logger.info("/activity-in获取到的actId为："+actId + "###uuid为" +uuid);
		String result = memberMessageService.activityIn(actId,uuid);
//		info = basicInfoService.updateBasicInfo(info);
		return new ResponseEntity<String>(result, HttpStatus.OK);
	}
	
	@RequestMapping(value="/orders", method=RequestMethod.GET)
	public String getOrders(HttpServletRequest request,String code,Model model,String lguuid) {
		logger.info("/orders获取到的code为："+code );
		if(null==code&&null==lguuid) {
			return "errorMessage";
		}
		
		if(null!=lguuid&&!"".equals(lguuid)) {
			String uuid = redisService.get("openid"+lguuid, String.class);
			List<ResvBase> resvList = dbResvervationService.searchFromEdp(uuid);
			model.addAttribute("resvList", resvList);
			return "orders";
		}else {
			String openid = loginService.getOpenId(code);
			model.addAttribute("openid", openid);
			model.addAttribute("beforepage", "orders");
			model.addAttribute("bookingUrl", MemberConfig.MOBILE_BOOKING_URL);
			String uuid = redisService.get("openid"+openid, String.class);
			if(null!=uuid&&!"".equals(uuid)) {
				List<ResvBase> resvList = dbResvervationService.searchFromEdp(uuid);
				model.addAttribute("resvList", resvList);
				return "orders";
			}else {
				return "redirect:https://m.lhw.cn/leaders-club/benefits";
			}
		}
	}
	@ResponseBody
	@RequestMapping(value="/forgetPassword", method=RequestMethod.GET)
	public ResponseEntity<Boolean> forgetPassword(String email) {
		logger.info("/forgetPassword获取到的email为："+email );
		boolean result = idmAuthService.forgetPassword(email);
		return new ResponseEntity<Boolean>(result, HttpStatus.OK);
	}

	@CrossOrigin(origins = "*")
	@ResponseBody
	@RequestMapping(value="/getState", method=RequestMethod.GET)
	public ResponseEntity<List<MemberState>> getState(String countryCode) {
		logger.info("/getState获取到的countryCode为："+countryCode );
		List<MemberState> result = memberStateService.search(countryCode);
		return new ResponseEntity<List<MemberState>>(result, HttpStatus.OK);
	}

	@CrossOrigin(origins = "*")
	@ResponseBody
	@RequestMapping(value="/cn2Spell", method=RequestMethod.POST)
	public ResponseEntity<ArrayList<ArrayList<String>>> cn2Spell(HttpServletRequest request,ModelMap model,String in,String type) {
		char[] inArray = in.toCharArray();
		String inString = "";
		for(char inChar:inArray) {
			//判断是否为中文，中文加空格，英文不加空格
			if(inChar >= 0x4E00 &&  inChar <= 0x9FA5) {
				inString = inString+inChar +" ";
			}else {
				inString = inString+inChar;
			}
			
		}
		inString = inString.trim();
		
		ArrayList<ArrayList<String>> ret = PinyinUtils.getHanyuPinyinStringArra(inString, type);
		return new ResponseEntity<ArrayList<ArrayList<String>>>(ret, HttpStatus.OK);
	}
	@CrossOrigin(origins = "*")
	@ResponseBody
	@RequestMapping(value="/updateInfo", method=RequestMethod.POST)
	public ResponseEntity<BasicInfo> updateBasicInfo(HttpServletRequest request,String address,String addressId,
			String birthDay,String businessTravel,String leisureTravel,String city,String homeTel,
			String homeTelId,String language,String nation,String officeTel,String officeTelId,
			String phone,String phoneTelId,String postCode,String preferredContact,String prefix,
//			boolean subscribeEmail,boolean subscribePromo,boolean subscribeSpecial,
			String marketing,String uuid,String state) {
		BasicInfo info = new BasicInfo();

        info.setPrefix(prefix);
        info.setAddress(address);
		info.setAddressId(addressId);
		info.setBirthDay(birthDay);
		info.setLanguage(language);
		info.setPhoneTel(phone);
		info.setPhoneTelId(phoneTelId);
		info.setUuid(uuid);
		info.setBusinessTravel(businessTravel);
		info.setLeisureTravel(leisureTravel);
		info.setCity(city);
		info.setHomeTel(homeTel);
		info.setHomeTelId(homeTelId);
		info.setNation(nation);
		info.setOfficeTel(officeTel);
		info.setOfficeTelId(officeTelId);
		info.setPostCode(postCode);
		info.setPreferredContact(preferredContact);
		info.setState(state);
//		info.setSubscribeEmail(subscribeEmail);
//		info.setSubscribePromo(subscribePromo);
//		info.setSubscribeSpecial(subscribeSpecial);
		info.setMarketing(marketing);
		
		info = basicInfoService.updateBasicInfo(info);
		logger.info("/updateInfo更新个人资料："+info.toString() );

		//独立更新 address
		AddressInbound addressInbound = new AddressInbound();
		addressInbound.setAddress1(info.getAddress());
		addressInbound.setAddress2(info.getAddress());
		addressInbound.setAddresstype("home");
		addressInbound.setCity(info.getCity());
		addressInbound.setCountry(info.getNation());
		addressInbound.setState(info.getState());
		addressInbound.setZipcode(info.getPostCode());
		Address addressInfo = aimiaService.updateAddress(uuid, info.getAddressId(), addressInbound);
		logger.info("/updateInfo更新个人资料：addressInfo="+addressInfo.toString() );
		return new ResponseEntity<BasicInfo>(info, HttpStatus.OK);
	}

	@ResponseBody
	@RequestMapping(value="/client/login", method=RequestMethod.GET)
	public ResponseEntity<MemberInfoResult> clientLogin(String email,String pwd) {
		MemberInfoResult infoResult = new MemberInfoResult();
		logger.info("client/login登录：email="+email + URLDecoder.decode(pwd) );
//		pwd = "aq1SW@de3";
//		email = "<EMAIL>";
		String uuid = loginService.loginIn(email, pwd);
		if(null!=uuid) {
			uuid = uuid.trim();
			redisService.addForever("uuidforemail"+uuid, email);
			
			try {
				loginLogService.add(uuid,email,"website");
			} catch (Exception e) {
				logger.error("添加登录log日志异常" + email );
			}
			infoResult = basicInfoService.getMemberInfo(uuid);
			//判断BonusAward 20211203 Simon BEGIN
			if(basicInfoService.setBonusCheck(uuid)){
				memberService.setIssueInteraction(uuid);
			}
			//判断BonusAward 20211203 Simon END

			infoResult.setCode(SUCCESS);
//			List<LoginLog> logItems = loginLogService.selectByUuid(uuid);f
//			for(LoginLog logItem : logItems) {
//				if(!logItem.getOpenid().equals("website")) {
//					infoResult.setIsWeixinLogined(true);
//					break;
//				}
//			}
		}else {
			infoResult.setCode(FAIL);
			logger.error("client/login登录失败：email="+email+";pwd="+pwd );
		}
		return new ResponseEntity<MemberInfoResult>(infoResult, HttpStatus.OK);
	}
	
	@ResponseBody
	@RequestMapping(value="/client/reloadInfo", method=RequestMethod.GET)
	public ResponseEntity<MemberInfoResult> reloadInfo(String uuid) {
		MemberInfoResult infoResult = new MemberInfoResult();
		if(null!=uuid&&!"".equals(uuid)) {
			infoResult = basicInfoService.getMemberInfo(uuid);
			infoResult.setCode(SUCCESS);
		}else {
			infoResult.setCode(FAIL);
		}
		return new ResponseEntity<MemberInfoResult>(infoResult, HttpStatus.OK);
	}
	
	@ResponseBody
	@RequestMapping(value="/client/activity", method=RequestMethod.GET)
	public ResponseEntity<List<MemberActivity>> clientActivity(String uuid) {
		List<MemberActivity> activityList = memberMessageService.getMemberMessage(uuid);
		return new ResponseEntity<List<MemberActivity>>(activityList, HttpStatus.OK);
	}
	@ResponseBody
	@RequestMapping(value="/client/getEdpReservation", method=RequestMethod.GET)
	public ResponseEntity<EdpReservation> getEdpReservation(String confirmNumber) {
		EdpReservation reservation = dbResvervationService.searchByConfirmNumberFromEdp(confirmNumber);
		return new ResponseEntity<EdpReservation>(reservation, HttpStatus.OK);
	}
	@ResponseBody
	@RequestMapping(value="/client/activity/{uuid}/{actId}", method=RequestMethod.GET)
	public ResponseEntity<String> clientActivity(@PathVariable("actId") String actId,
			@PathVariable("uuid") String uuid) {
		System.out.println(actId);
		System.out.println(uuid);
		String result = memberMessageService.activityIn(actId,uuid);
		return new ResponseEntity<String>(result, HttpStatus.OK);
	}
	
	
	
	@RequestMapping(value="/test", method=RequestMethod.GET)
	public String getHelpsqrcodde(Model model) {
		model.addAttribute("openid", "12345");
		model.addAttribute("beforepage", "basic-info");
		return "testqrcode";
	}
	
	@RequestMapping(value="/testqw", method=RequestMethod.GET)
	public String getHelpsqrascodde(Model model) {
		return "qrcodeLoginClosed";
	}
	@RequestMapping(value="/qrcode/login", method=RequestMethod.GET)
	public String qrcodeLogin(Model model,String unionId,String code) {
		
		System.out.println(unionId);
		System.out.println(code);
		Gson gson = new Gson();
		String openid = loginService.getOpenId(code);
		
		String uuid = redisService.get("openid"+openid, String.class);
		if(null!=uuid&&!"".equals(uuid)) {
			System.out.println("获取会员信息uuid+"+uuid);
			MemberInfoResult info = basicInfoService.getMemberInfo(uuid);
			info.setCode(SUCCESS);
			redisService.add("qr_"+unionId, gson.toJson(info));
			model.addAttribute("success", "true");
		}else {
			MemberInfoResult info = new MemberInfoResult();
			info.setCode(FAIL);
			redisService.add("qr_"+unionId, gson.toJson(info));
			model.addAttribute("success", "false");
		}
		
		return "qrcodeLoginClosed";
	}
	@CrossOrigin(origins = "*", maxAge = 3600)
	@ResponseBody
	@RequestMapping(value="/qrcode/mlogin", method=RequestMethod.GET)
	public ResponseEntity<MemberInfoResult> qrcodeMLogin(Model model,String code) {
		MemberInfoResult info = new MemberInfoResult();
		logger.info("/qrcode/mlogin手机端微信登录：code="+code );
		
		String openid = loginService.getOpenId(code);
		
		String uuid = redisService.get("openid"+openid, String.class);
		if(null!=uuid&&!"".equals(uuid)) {
			info = basicInfoService.getMemberInfo(uuid);
			info.setCode(SUCCESS);
		}else {
			info = new MemberInfoResult();
			info.setCode(FAIL);
			logger.info("/qrcode/mlogin手机端微信登录失败：code="+code );
		}
		
		return new ResponseEntity<MemberInfoResult>(info, HttpStatus.OK);
	}
	
//	//判断是否为汉字
//	public boolean vd(char a){
//		System.out.println(a);
//		boolean isCn = false;
//		if(a >= 0x4E00 &&  a <= 0x9FA5) {
//			isCn = true;
//		}else {
//			isCn = false;
//		}
//		System.out.println(isCn);
//		return isCn;
//	}
}
