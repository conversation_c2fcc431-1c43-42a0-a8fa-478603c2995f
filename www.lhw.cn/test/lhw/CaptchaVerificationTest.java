package lhw;

import net.hubs1.lhw.utils.CaptchaVerificationUtil;

/**
 * 验证码验证工具类测试
 */
public class CaptchaVerificationTest {
    
    public static void main(String[] args) {
        CaptchaVerificationUtil util = new CaptchaVerificationUtil();
        
        // 测试验证码验证
        System.out.println("开始测试验证码验证...");
        
        // 测试1: 正常参数
        String testCaptchaParam = "test_captcha_param";
        String testSceneId = "test_scene";
        
        CaptchaVerificationUtil.CaptchaVerifyResult result = util.verifyCaptcha(testCaptchaParam, testSceneId);
        
        System.out.println("测试结果:");
        System.out.println("Success: " + result.isSuccess());
        System.out.println("Message: " + result.getMessage());
        System.out.println("Code: " + result.getCode());
        System.out.println("VerifyCode: " + result.getVerifyCode());
        System.out.println("VerifyResult: " + result.getVerifyResult());
        
        // 测试2: 空参数
        System.out.println("\n测试空参数:");
        CaptchaVerificationUtil.CaptchaVerifyResult emptyResult = util.verifyCaptcha("", null);
        System.out.println("Empty param result - Success: " + emptyResult.isSuccess() + ", Message: " + emptyResult.getMessage());
        
        // 测试3: null参数
        System.out.println("\n测试null参数:");
        CaptchaVerificationUtil.CaptchaVerifyResult nullResult = util.verifyCaptcha(null, null);
        System.out.println("Null param result - Success: " + nullResult.isSuccess() + ", Message: " + nullResult.getMessage());
        
        System.out.println("\n测试完成!");
    }
}
