package net.hubs1.lhw.utils;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import net.hubs1.lhw.api.HttpClient;

/**
 * 验证码验证工具类
 * 调用外部验证服务进行验证码验证
 */
@Component
public class CaptchaVerificationUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(CaptchaVerificationUtil.class);
    
    /**
     * 外部验证服务URL
     */
    private static final String CAPTCHA_VERIFY_URL = "http://127.0.0.1:8000/api/v1/aliyun/captcha";
    
    /**
     * 验证码验证结果类
     */
    public static class CaptchaVerifyResult {
        private boolean success;
        private String message;
        private String code;
        private String verifyCode;
        private Boolean verifyResult;
        
        public CaptchaVerifyResult() {}
        
        public CaptchaVerifyResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public void setSuccess(boolean success) {
            this.success = success;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
        
        public String getCode() {
            return code;
        }
        
        public void setCode(String code) {
            this.code = code;
        }
        
        public String getVerifyCode() {
            return verifyCode;
        }
        
        public void setVerifyCode(String verifyCode) {
            this.verifyCode = verifyCode;
        }
        
        public Boolean getVerifyResult() {
            return verifyResult;
        }
        
        public void setVerifyResult(Boolean verifyResult) {
            this.verifyResult = verifyResult;
        }
    }
    
    /**
     * 验证验证码
     *
     * @param captchaVerifyParam 验证码验证参数
     * @param sceneId 场景ID（可选）
     * @return 验证结果
     */
    public CaptchaVerifyResult verifyCaptcha(String captchaVerifyParam, String sceneId) {
        if (StringUtils.isEmpty(captchaVerifyParam)) {
            logger.warn("验证码参数为空");
            return new CaptchaVerifyResult(false, "验证码参数不能为空");
        }

        try {
            // 构建GET请求URL
            StringBuilder urlBuilder = new StringBuilder(CAPTCHA_VERIFY_URL);
            urlBuilder.append("?captchaVerifyParam=").append(captchaVerifyParam);
            if (StringUtils.isNotEmpty(sceneId)) {
                urlBuilder.append("&sceneId=").append(sceneId);
            }

            String requestUrl = urlBuilder.toString();
            logger.info("调用外部验证服务，请求URL: {}", requestUrl);

            // 调用外部验证服务
            String response = HttpClient.getHttpXml(requestUrl);

            if (StringUtils.isEmpty(response)) {
                logger.error("外部验证服务返回空响应");
                return new CaptchaVerifyResult(true, "验证服务异常，默认通过验证");
            }

            logger.info("外部验证服务响应: {}", response);

            // 解析响应
            JSONObject responseJson = JSON.parseObject(response);
            CaptchaVerifyResult result = new CaptchaVerifyResult();

            // 根据响应结构解析结果
            if (responseJson.containsKey("success")) {
                result.setSuccess(responseJson.getBooleanValue("success"));
            } else if (responseJson.containsKey("verifyResult")) {
                result.setSuccess(responseJson.getBooleanValue("verifyResult"));
                result.setVerifyResult(responseJson.getBoolean("verifyResult"));
            } else {
                // 如果响应格式不符合预期，默认通过验证
                logger.warn("外部验证服务响应格式异常，默认通过验证: {}", response);
                result.setSuccess(true);
                result.setMessage("验证服务响应格式异常，默认通过验证");
                return result;
            }

            result.setMessage(responseJson.getString("message"));
            result.setCode(responseJson.getString("code"));
            result.setVerifyCode(responseJson.getString("verifyCode"));

            if (responseJson.containsKey("verifyResult")) {
                result.setVerifyResult(responseJson.getBoolean("verifyResult"));
            }

            return result;

        } catch (Exception e) {
            logger.error("调用外部验证服务异常，默认通过验证", e);
            return new CaptchaVerifyResult(true, "验证服务异常，默认通过验证: " + e.getMessage());
        }
    }
}
