package net.hubs1.lhw.api;

import java.nio.charset.Charset;
import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import com.itcrowd.framework.util.LogUtil;

public class HttpClient {

	private final static Log log = LogFactory.getLog(HttpClient.class);

	private static CloseableHttpClient httpclient;

	private final static int TRY_TIMES = 3;

	static {
		initHttpClientPool();
	}

	private static void initHttpClientPool() {
		PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
		cm.setMaxTotal(200);
		cm.setDefaultMaxPerRoute(20);
		httpclient = HttpClients.custom().setConnectionManager(cm).build();
	}

	public static String postHttpXml( List<NameValuePair> params, String url) {
		String response = "";
		try {
			HttpPost httpPost = new HttpPost(url);
			httpPost.setEntity(new UrlEncodedFormEntity(params));

			int times = 0;
			while (times < TRY_TIMES) {
				times ++;
				try {
					HttpResponse httpResponse = httpclient.execute(httpPost);
					response = EntityUtils.toString(httpResponse.getEntity(),  Charset.forName("UTF-8"));
					times = TRY_TIMES +100;
				} catch (org.apache.http.NoHttpResponseException e) {
				     log.error("NoHttpResponseException "+times+" times !");
				     Thread.sleep(1000);
				} catch (Exception e) {
					 throw e;
				}
			}
		} catch (Exception e) {
			LogUtil.error(log, "postHttpXml", e, e.getMessage(), params);
		}
		log.debug("postHttpXml Response \r\n " + response);
		return response;
	}

	public static String getHttpXml(String url) {
		String response = "";
		RequestConfig config = RequestConfig.copy(RequestConfig.DEFAULT).setConnectTimeout(15000)
				.setSocketTimeout(60000).build();
		HttpGet httpGet = new HttpGet(url);
		httpGet.setConfig(config);
		try {
			HttpResponse httpResponse = httpclient.execute(httpGet);
			response = EntityUtils.toString(httpResponse.getEntity(), "UTF-8");
		} catch (Exception e) {
			LogUtil.error(log, "member getHttpXml", e, e.getMessage(), url);
		}
		httpGet.completed();
		return response;
	}

	public static String postHttpJson(JsonObject params, String url, String token) {
		String response = "";
		try {
			HttpPost httpPost = new HttpPost(url);
			httpPost.addHeader("Content-Type", "application/json;charset=UTF-8");
			httpPost.setHeader("Authorization", token);
			StringEntity stringEntity = new StringEntity(params.toString(), "UTF-8");
			stringEntity.setContentEncoding("UTF-8");
			httpPost.setEntity(stringEntity);

			int times = 0;
			while (times < TRY_TIMES) {
				times ++;
				try {
					HttpResponse httpResponse = httpclient.execute(httpPost);
					response = EntityUtils.toString(httpResponse.getEntity(),  Charset.forName("UTF-8"));
					times = TRY_TIMES +100;
				} catch (org.apache.http.NoHttpResponseException e) {
					log.error("NoHttpResponseException "+times+" times !");
					Thread.sleep(1000);
				} catch (Exception e) {
					throw e;
				}
			}
		} catch (Exception e) {
			LogUtil.error(log, "postHttpXml", e, e.getMessage(), params);
		}
		log.debug("postHttpXml Response \r\n " + response);
		return response;
	}

	public static String postHttp(String jsonBody, String url) {
		String response = "";
		try {
			HttpPost httpPost = new HttpPost(url);
			httpPost.addHeader("Content-Type", "application/json;charset=UTF-8");
			StringEntity stringEntity = new StringEntity(jsonBody, "UTF-8");
			stringEntity.setContentEncoding("UTF-8");
			httpPost.setEntity(stringEntity);

			int times = 0;
			while (times < TRY_TIMES) {
				times ++;
				try {
					HttpResponse httpResponse = httpclient.execute(httpPost);
					response = EntityUtils.toString(httpResponse.getEntity(),  Charset.forName("UTF-8"));
					times = TRY_TIMES +100;
				} catch (org.apache.http.NoHttpResponseException e) {
					log.error("NoHttpResponseException "+times+" times !");
					Thread.sleep(1000);
				} catch (Exception e) {
					throw e;
				}
			}
		} catch (Exception e) {
			LogUtil.error(log, "postHttp", e, e.getMessage(), jsonBody);
		}
		log.debug("postHttp Response \r\n " + response);
		return response;
	}

}
