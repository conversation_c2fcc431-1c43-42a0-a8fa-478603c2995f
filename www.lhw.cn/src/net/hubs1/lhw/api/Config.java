package net.hubs1.lhw.api;

import java.io.IOException;
import java.util.Properties;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PropertiesLoaderUtils;

public class Config {

	private final static Log log = LogFactory.getLog(Config.class);

	public static String MEMBER_URL_PREFIX;

	public static String SBE_URL_PREFIX;

	public static String SBE_CRM_URL_PREFIX;
	public static String SBE_CRM_TOKEN;

	public static String EDP_HOTEL_DIRECT_URL;

	public static String EDP_HOTEL_DIRECT_AUTH;



	static {
		initLoad();
	}

	private static void initLoad() {
		Resource resource = new ClassPathResource("/config.properties");
		try {
			Properties props = PropertiesLoaderUtils.loadProperties(resource);
			MEMBER_URL_PREFIX = (String) props.get("member.url.prefix");
			SBE_URL_PREFIX = (String) props.get("url.sbe-api");
			SBE_CRM_URL_PREFIX = (String) props.get("url.sbe-crm-api");
			SBE_CRM_TOKEN = (String) props.get("url.sbe-crm-api-token");
			EDP_HOTEL_DIRECT_URL = (String) props.get("member.edp.HOTEL_DIRECT_URL");
			EDP_HOTEL_DIRECT_AUTH = (String) props.get("member.edp.HOTEL_DIRECT_AUTH");
		} catch (IOException e) {
			log.fatal("Miss config.properties , exception " + e);
		}
	}

}
