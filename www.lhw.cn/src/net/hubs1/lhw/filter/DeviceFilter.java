package net.hubs1.lhw.filter;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.mobile.device.Device;
import org.springframework.mobile.device.LiteDeviceResolver;

import cn.lhw.leadinglink.LeadingLinkUtil;

public class DeviceFilter implements Filter {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        // 促销代码处理
        String promotionCode = ((HttpServletRequest) request).getParameter("ext");
        if (promotionCode != null && !"".equals(promotionCode)) {
            Cookie cookie = new Cookie("promotionCode", promotionCode);
            cookie.setMaxAge(24 * 60 * 60);
            cookie.setPath("/");
            cookie.setDomain(".lhw.cn");
            ((HttpServletResponse) response).addCookie(cookie);
        }
        System.out.println("cookie ext=" + this.getCookie(((HttpServletRequest) request), "promotionCode"));

        LiteDeviceResolver liteDeviceResolver = new LiteDeviceResolver();

        Device device = liteDeviceResolver.resolveDevice((HttpServletRequest) request);

        String srcUrl = ((HttpServletRequest) request).getRequestURI();// /web/leadinglink/Ahn-Luh-Zhujiajiao
        System.out.println(srcUrl);
        logger.info("srcUrl 1=" + srcUrl);
        System.out.println(((HttpServletRequest) request).getParameter("indate"));
        String serverName = ((HttpServletRequest) request).getServerName();// www.lhw.cn
        String bookingIndex = LeadingLinkUtil.getBookingIndexByBookingdomain(serverName);
        logger.info("serverName 1=" + serverName);
        if (srcUrl.contains("/leaders-club-hub") || srcUrl.contains("/crm") || srcUrl.contains("forgetPassword") || srcUrl.contains("checkRegisterEmail") || srcUrl.contains("webstatic")){
            chain.doFilter(request, response);
            return;
        }
        if (device.isMobile()) {
            logger.info("isMobile srcUrl=" + srcUrl);
            if (srcUrl.endsWith("/details")) {
                String queryString = ((HttpServletRequest) request).getQueryString();
                String url = "";
                if (bookingIndex == null) {
                    serverName = "m.lhw.cn";
                }

                if (null != queryString && "" != queryString) {
                    url = "http://" + serverName + srcUrl + "?" + queryString;
                } else {
                    url = "http://" + serverName + srcUrl;
                }
                ((HttpServletResponse) response).sendRedirect(url);

                return;
            }

            if (srcUrl.startsWith("/register.html") || srcUrl.startsWith("/register")) {
                String url = "http://m.lhw.cn" + srcUrl;
                ((HttpServletResponse) response).sendRedirect(url);
                return;
            }

            if (srcUrl.startsWith("/offers")) {
                String url = "http://m.lhw.cn" + srcUrl;
                ((HttpServletResponse) response).sendRedirect(url);
                return;
            }

            if (srcUrl.startsWith("/promotion")) {
                String url = "http://m.lhw.cn" + srcUrl;
                ((HttpServletResponse) response).sendRedirect(url);
                return;
            }


            if (srcUrl.startsWith("/hotel-search/") || srcUrl.startsWith("/hotel/")) {
                if (StringUtils.isNotBlank(((HttpServletRequest) request).getParameter("indate"))
                        && StringUtils.isNotBlank(((HttpServletRequest) request).getParameter("outdate"))) {
                    String[] urlParams = srcUrl.split("/");
                    String hotelInfo = urlParams[urlParams.length - 2];
                    String[] hotelInfos = hotelInfo.split("-");
                    srcUrl = srcUrl.replace(hotelInfos[0] + "-", "");
                    String urlParamStr = "inDate=" + ((HttpServletRequest) request).getParameter("indate") + "&outDate="
                            + ((HttpServletRequest) request).getParameter("outdate");
                    String url = "http://m.lhw.cn/m" + srcUrl + "?" + urlParamStr;
                    ((HttpServletResponse) response).sendRedirect(url);
                } else {
                    String url = "http://m.lhw.cn/m" + srcUrl;
                    System.out.println(url);
                    ((HttpServletResponse) response).sendRedirect(url);
                }

                return;
            }

            if (srcUrl.startsWith("/leaders-club/")) {
                String url = "http://m.lhw.cn" + srcUrl;
                ((HttpServletResponse) response).sendRedirect(url);
                return;
            }

            if (srcUrl.startsWith("/leaders-club")) {
                String url = "http://m.lhw.cn/m" + srcUrl;
                ((HttpServletResponse) response).sendRedirect(url);
                return;
            }

            if (srcUrl.startsWith("/vaildate/tokenKey")) {
                String url = "http://m.lhw.cn/m" + srcUrl;
                ((HttpServletResponse) response).sendRedirect(url);
                return;
            }

            if (srcUrl.startsWith("/privacy-policy") || srcUrl.startsWith("/terms-and-conditions")) {
                String url = "http://m.lhw.cn" + srcUrl;
                ((HttpServletResponse) response).sendRedirect(url);
                return;
            }
            if (srcUrl.startsWith("/site-map")) {
                String url = "http://m.lhw.cn/m/city";
                ((HttpServletResponse) response).sendRedirect(url);
                return;
            }

            if (srcUrl.startsWith("/web/leadinglink")) {
                srcUrl = srcUrl.replace("/web/", "/m/");
                String queryString = ((HttpServletRequest) request).getQueryString();
                String url = "";

                if (bookingIndex == null) {
                    serverName = "m.lhw.cn";
                }

                if (null != queryString && "" != queryString) {
                    url = "http://" + serverName + srcUrl + "?" + queryString;
                } else {
                    url = "http://" + serverName + srcUrl;
                }
                ((HttpServletResponse) response).sendRedirect(url);
                return;
            }

            if (srcUrl.startsWith("/double-11")) {
                String url = "http://m.lhw.cn" + srcUrl;
                ((HttpServletResponse) response).sendRedirect(url);
                return;
            }


            if (bookingIndex != null && (srcUrl == null || srcUrl.equals("") || srcUrl.equals("/"))) {
                ((HttpServletResponse) response).sendRedirect("/m" + bookingIndex);
                return;
            }
            ((HttpServletResponse) response).sendRedirect("http://m.lhw.cn");
            return;

        } else {
            if (bookingIndex != null && (srcUrl == null || srcUrl.equals("") || srcUrl.equals("/"))) {
                logger.info(srcUrl + "不跳转 web");
                ((HttpServletResponse) response).sendRedirect("/web" + bookingIndex);
                return;
            }

            chain.doFilter(request, response);
        }
    }

    @Override
    public void destroy() {

    }

    private String getCookie(HttpServletRequest request, String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        Cookie[] cookies = request.getCookies();
        // cookies不为空
        if (cookies != null) {
            for (int i = 0; i < cookies.length; i++) {
                if (key.equals(cookies[i].getName())) {
                    return cookies[i].getValue();
                }
            }
        }
        return null;
    }
}
