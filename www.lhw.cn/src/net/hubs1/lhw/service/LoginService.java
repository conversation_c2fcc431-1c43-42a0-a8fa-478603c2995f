package net.hubs1.lhw.service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import net.hubs1.lhw.api.HttpClient;
import net.hubs1.lhw.api.Config;
import net.hubs1.lhw.model.MemberActivityResult;
import net.hubs1.lhw.model.MemberInfoResult;
import net.hubs1.lhw.redis.RedisService;

@Service
public class LoginService {

	private Logger logger = LoggerFactory.getLogger(this.getClass());
	
	private static String URL_PREFIX = Config.MEMBER_URL_PREFIX;

	@Autowired
	RedisService redisService;
	/**
	 * 登录接口
	 * 
	 * @param username
	 * @param password
	 * @return
	 */
	public MemberInfoResult login(String username, String password) {
		MemberInfoResult result = null;
		try {
			//password 进行url编码,防止特殊字符被过滤
			password = java.net.URLEncoder.encode(password, "utf-8");
			String urlPath = URL_PREFIX + "/client/login?email=" + username + "&pwd="
					+ password;
			// 调登录接口
			String respose = HttpClient.getHttpXml(urlPath);
			logger.info("login urlPath:" +  urlPath);

			logger.info("login response{}", respose);
			result = parseLogin(respose);
		} catch (Exception e) {
			logger.error("login error: {}", e);
		}
		return result;
	}
	
	public MemberInfoResult getUserInfo(String uuid) {
		MemberInfoResult result = null;
		try {
			String urlPath = URL_PREFIX + "/client/reloadInfo?uuid=" + uuid;
			// 调登录接口
			String respose = HttpClient.getHttpXml(urlPath);
			logger.info("login response{}", respose);
			result = parseLogin(respose);
		} catch (Exception e) {
			logger.error("login error: {}", e);
		}
		return result;
	}
	
	public MemberInfoResult qrlogin(String uuidId) {
		MemberInfoResult result = null;
		String loginString = redisService.get("qr_"+uuidId, String.class);
		result = parseLogin(loginString);
		System.out.println(loginString);
		return result;
	}
	//重置密码验证token获取email
	public String validateToken(String tokenKey,String token) {
		String email = null;
		try {
			String urlPath = URL_PREFIX + "/vaildate/tokenKey/"+tokenKey+"/token/"+token;
			// 调登录接口
			String respose = HttpClient.getHttpXml(urlPath);
			logger.info("validateToken response{}", respose);
			email = respose;
		} catch (Exception e) {
			logger.error("validateToken error: {}", e);
		}
		return email; 
	}
	
	//重置密码
	public boolean resetPassword(String email,String password) {
		boolean isSuccess = false;
		try {
			String urlPath = URL_PREFIX + "/reset/email/"+email+"/password/"+password;
			// 调登录接口
			String respose = HttpClient.getHttpXml(urlPath);
			logger.info("validateToken response{}", respose);
			isSuccess = Boolean.parseBoolean(respose);
		} catch (Exception e) {
			logger.error("validateToken error: {}", e);
		}
		return isSuccess;
	}

	/**
	 * 会员等级Club、Sterling & Aurelian
	 * 
	 * @param respose
	 * @return
	 */
	private MemberInfoResult parseLogin(String respose) {
		return new Gson().fromJson(respose, MemberInfoResult.class);
	}

	/**
	 * 获取活动信息
	 * 
	 * @return
	 */
	public MemberActivityResult getActivity(String uuid) {
		MemberActivityResult result = null;
		try {
			String key = "member_activity_" + uuid;
			result = redisService.get(key, MemberActivityResult.class);
			if (result == null) {
				String urlPath = URL_PREFIX + "/client/activity?uuid=" + uuid;
				// 调登录接口
				String respose = HttpClient.getHttpXml(urlPath);
				logger.info("getActivity response{}", respose);
				List<MemberActivityResult> results = parseActivity(respose);
				if (results != null) {
					if (results.size() > 0) {
						result = results.get(0);
						redisService.add(key, result, 60);
					} else {
						result = new MemberActivityResult();
						result.setActId("-1");
						redisService.add(key, result, 60);
					}
				}
			}
			if ("-1".equals(result.getActId())) {
				result = null;
			}
		} catch (Exception e) {
			logger.error("login error: {}", e);
		}
		return result;
	}

	private List<MemberActivityResult> parseActivity(String respose) {
		return jsonToList(respose, MemberActivityResult[].class);
	}
	
	public Map<String, String> partakeActivity(String uuid, String actId) {
		Map<String,String> result = null;
		try {
			String urlPath = URL_PREFIX + "/client/activity/" + uuid + "/" + actId;
			// 调登录接口
			String respose = HttpClient.getHttpXml(urlPath);
			logger.info("partakeActivity response: {}", respose);
			result = new HashMap<String, String>();
			result.put("code", respose);
		} catch (Exception e) {
			logger.error("login error: {}", e);
		}
		return result;
	}
	
	public String forgetPassword(String email) {
		String respose = null;
		try {
			String url = URL_PREFIX + "/forgetPassword?email=" + email;
			// 调登录接口
			respose = HttpClient.getHttpXml(url);
			logger.info("forgetPassword response: {}", respose);
		} catch (Exception e) {
			logger.error("forgetPassword error: {}", e);
		}
		return respose;
	}
	
	public String checkIsCorrectEmail(String email) {
		String respose = null;
		try {
			String url = URL_PREFIX + "/checkIsCorrectEmail?email=" + email;
			// 调登录接口
			respose = HttpClient.getHttpXml(url);
			logger.info("checkIsCorrectEmail response: {}", respose);
		} catch (Exception e) {
			logger.error("checkIsCorrectEmail error: {}", e);
		}
		return respose;
	}

	/**
	 * 将Json数组解析成相应的映射对象列表
	 * @param json
	 * @param clazz
	 * @return
	 */
	private static <T> List<T> jsonToList(String json, Class<T[]> clazz) {
		Gson gson = new Gson();
		T[] array = gson.fromJson(json, clazz);
		return Arrays.asList(array);
	}

	/**
	 * 将Json数组解析成相应的映射对象列表
	 * @param jsonData
	 * @param type
	 * @return
	 */
	static <T> List<T> parseJsonArrayWithGson(String jsonData, Class<T> type) {
		Gson gson = new Gson();
		List<T> result = gson.fromJson(jsonData, new TypeToken<List<T>>(){}.getType());
		return result;
	}


}
