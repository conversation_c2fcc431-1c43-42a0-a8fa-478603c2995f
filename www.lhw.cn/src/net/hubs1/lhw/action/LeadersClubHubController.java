package net.hubs1.lhw.action;

import cn.lhw.cms.api.LhwCmsApi;
import cn.lhw.cms.entity.ClubHotel;
import net.hubs1.lhw.api.Config;
import net.hubs1.lhw.model.*;
import net.hubs1.lhw.service.LoginService;
import net.hubs1.lhw.service.RegisterService;
import net.hubs1.lhw.service.SBECrmService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * Leaders CLUB hub
 */
@Controller
public class LeadersClubHubController {

    private  Logger logger = LoggerFactory.getLogger(this.getClass());

    private   String SBE_URL = Config.SBE_URL_PREFIX;

    @Autowired
    private LoginService loginService;

    @Autowired
    private SBECrmService sbeCrmService;

    @Autowired
    private RegisterService registerService;
    @Autowired
    LhwCmsApi cms;

    @RequestMapping(value = "/leaders-club-hub")
    public ModelAndView index(@RequestParam String hotel) {
        ModelAndView view = new ModelAndView();
        ClubHotel clubHotel = cms.getClubHotelBySabreCode(hotel);
        view.addObject("hotel", clubHotel);
        view.setViewName("leaders-club-hub/index");
        return view;
    }

    @RequestMapping(value = "/crm/enrollment")
    public ModelAndView enrollment(@RequestParam String hotel, HttpServletRequest request, HttpServletResponse response) {
        ModelAndView view = new ModelAndView();
        HttpSession session = request.getSession();
        Cookie cookie = new Cookie("JSESSIONID", session.getId());
        view.addObject("hotel", cms.getClubHotelBySabreCode(hotel));
        cookie.setPath("/");
        response.addCookie(cookie);
        Object user = session.getAttribute("user");
        view.addObject("SBE_URL", SBE_URL);
        if (user == null) {
            view.setViewName("leaders-club-hub/enrollment");
        } else {
            view.addObject("user", user);
            view.addObject("token", session.getAttribute("token"));
            view.setViewName("leaders-club-hub/success");
        }
        return view;
    }

    @RequestMapping(value = "/crm/do_enrollment_save", method = RequestMethod.POST)
    public @ResponseBody String saveEnrollment(HttpServletRequest request, HttpServletResponse response) throws IOException, ExecutionException, InterruptedException {
        HttpSession session = request.getSession();
        Cookie cookie = new Cookie("JSESSIONID", session.getId());
        cookie.setPath("/");
        response.addCookie(cookie);
        String hotel = request.getParameter("hotel");
        String email = request.getParameter("Email");
        String country = request.getParameter("Country");
        String city = request.getParameter("City");
        String password = request.getParameter("Password");
        String preferredLanguage = request.getParameter("PreferredLanguage");
        String prefix = request.getParameter("Prefix");
        String firstName = request.getParameter("FirstName_en");
        String lastName = request.getParameter("LastName_en");
        logger.info("password==" + password + "email==" + email + "hotel==" + hotel + "country==" + country + "city==" + city + "preferredLanguage==" + preferredLanguage + "prefix==" + prefix + "firstName==" + firstName + "lastName==" + lastName);

        CompletableFuture<HotelDirectMemberShipResult> hotelDirectMemberShipResultFeture  = CompletableFuture.supplyAsync(()-> registerService.registerEDPHotelDirectUser(email, password, hotel, "SBE",
                "CLUB", prefix, firstName, "", lastName, preferredLanguage, city, country));

        CompletableFuture<Void> Feature = CompletableFuture.allOf(hotelDirectMemberShipResultFeture);
        try {
            Feature.get(160, TimeUnit.SECONDS);
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            e.printStackTrace();
        }
        HotelDirectMemberShipResult hotelDirectMember = hotelDirectMemberShipResultFeture.get();
        if (hotelDirectMember.getObjectData() != null && hotelDirectMember.getCode() == 201){
            String uuid = hotelDirectMember.getObjectData().getUuid();
            if (uuid != null){
                MemberInfoResult memberInfoResult = loginService.getUserInfo(uuid);
                String token = loginCrm(uuid);
                if (token != null){
                    session.setAttribute("token", token);
                    session.setAttribute("user", memberInfoResult);
                    response.sendRedirect("/crm/enrollment?hotel=" + hotel + "&src=hub");
                }
            }
        }
        return "The hotel does not exist";
    }


    @RequestMapping(value = "/crm/signin")
    public ModelAndView signIn(@RequestParam String hotel, HttpServletRequest request, HttpServletResponse response, HttpSession session) {
        ModelAndView view = new ModelAndView();
        view.addObject("hotel", cms.getClubHotelBySabreCode(hotel));
        String token = (String) session.getAttribute("token");
        logger.info("登陆信息token" + token, token);
        if (token != null){
            view.addObject("token", token);
            view.addObject("SBE_URL", SBE_URL);
            view.setViewName("leaders-club-hub/jump");
        }else {
            view.setViewName("leaders-club-hub/signin");
        }
        return view;
    }

    @RequestMapping(value = "/crm/do-sign", method = RequestMethod.POST)
    public ResponseEntity<Integer> Login(@RequestParam String hotel, HttpServletRequest request,
                                        HttpServletResponse response, @RequestBody Map<String, String> map, HttpSession session) throws IOException {
        String email = map.get("email");
        String password = map.get("password");
        MemberInfoResult memberInfoResult = loginService.login(email, password);
        logger.info("登陆信息memberInfoResult", memberInfoResult.getUuid());
        logger.info("登陆信息session", session);
        String uuid = memberInfoResult.getUuid();
        int code = 403;
        if (uuid != null){
            session.setAttribute("user", memberInfoResult);
            code = 200;
            if (memberInfoResult.getRenew() == "Expired" || memberInfoResult.getRenew() == "Canceled"){
                code = 401;
            }
            if (code == 200) {
                String token = loginCrm(uuid);
                if (token != null){
                    logger.info("token is " + session.getMaxInactiveInterval(), token);
                    session.setAttribute("token", token);
                    return new ResponseEntity<Integer>(code, HttpStatus.OK);
                }

            }
        }

        return new ResponseEntity<Integer>(code,  HttpStatus.OK);
    }

    @RequestMapping(value = "/crm/jump")
    public ModelAndView JumpSEB(@RequestParam String hotel, HttpServletRequest request,
                                HttpServletResponse response, HttpSession session){
        ModelAndView view = new ModelAndView();
        view.addObject("hotel", cms.getClubHotelBySabreCode(hotel));
        view.addObject("SBE_URL", SBE_URL);
        view.addObject("token", session.getAttribute("token"));
        view.setViewName("leaders-club-hub/jump");
        return view;
    }

    @RequestMapping(value = "/crm/forgot-password")
    public ModelAndView ForgotPassword(@RequestParam String hotel) {
        ModelAndView view = new ModelAndView();
        view.addObject("hotel", cms.getClubHotelBySabreCode(hotel));
        view.setViewName("leaders-club-hub/forgot-password");
        return view;
    }

    private String loginCrm(String uuid){
        SBECrmResult sbeCrmResult = sbeCrmService.session("24447", "CRM", uuid,"Guest");
        logger.info("登陆信息sbeCrmResult" + sbeCrmResult.getProfileId(), sbeCrmResult);
        return sbeCrmResult.getAccessToken();
    }

    @RequestMapping(value = "/crm/logout", method = RequestMethod.GET)
    public String logout(HttpSession httpSession){
        httpSession.invalidate();
        return "ok";
    }
}
