<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<script>

    function getCookie(cname){
        var name = cname + "=";
        var ca = document.cookie.split(';');
        for(var i=0; i<ca.length; i++)
        {
            var c = ca[i].trim();
            if (c.indexOf(name)==0) return c.substring(name.length,c.length);
        }
        return "";
    }
    var dataLayer = window.dataLayer || [];
    var pathname = window.location.pathname;
    var userID = getCookie('lhw.cn_uuid');
    var userType = getCookie('lhw.cn_member_level');
    var dataLayerCopy_window = {
        "userID": userID,
        "pageCategory": "",
        "userType": userType,
        "HostName":"lhw.cn"
    }

    if(pathname.indexOf('/offers/') !== -1){
        dataLayerCopy_window.pageCategory = 'Offer Page'
    }else if(pathname === '/' || pathname === '/index.html'){
        dataLayerCopy_window.pageCategory = 'Homepage'
    }else if(pathname.indexOf('/promotion/') !== -1){
        dataLayerCopy_window.pageCategory = 'Promotion Page'
    }else if(pathname.indexOf('/hotel-search/') !== -1){
        dataLayerCopy_window.pageCategory = 'Search Result Page'
    }else if(pathname.indexOf('/hotel/') !== -1){
        dataLayerCopy_window.pageCategory = 'Product Detail Page'
    }else if(pathname.indexOf('/m/continent') !== -1 || pathname.indexOf('/m/country') !== -1 || pathname.indexOf('/m/select') !== -1){
        dataLayerCopy_window.pageCategory = 'Search Select Page'
    }else if(pathname.indexOf('/magnificent-journeys') !== -1){
        dataLayerCopy_window.pageCategory = 'Magnificent-journeys'
    }else if(pathname.indexOf('/leaders-club/press-center') !== -1){
        dataLayerCopy_window.pageCategory = 'Press center'
    }else if(pathname.indexOf('/leaders-club') !== -1){
        dataLayerCopy_window.pageCategory = 'Leaders-club'
    }else if(pathname.indexOf('/domestic') !== -1 || pathname.indexOf('/international') !== -1){
        dataLayerCopy_window.pageCategory = 'Hotel List Page'
    }else if(window.location.host ==='booking.lhw.cn'){
        dataLayerCopy_window.pageCategory = 'Booking System'
    }else{
        dataLayerCopy_window.pageCategory = 'Others'
    }
    if(dataLayerCopy_window.pageCategory){
        dataLayer.push(dataLayerCopy_window)
    }

    if(pathname.indexOf('register.html') === -1 && pathname.indexOf('leaders-club/benefits-booking') === -1){
        sessionStorage.removeItem("hotel_order_data");
    }




    (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-5N67ZZ5');</script>
<!-- End Google Tag Manager -->
<!-- Google Tag Manager (noscript) -->

<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5N67ZZ5"
                  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
<!-- Google Tag Manager -->
<script>
    !function(g,d,t,e,v,n,s){if(g.gdt)return;v=g.gdt=function(){v.tk?v.tk.apply(v,arguments):v.queue.push(arguments)};v.sv='1.0';v.bt=0;v.queue=[];n=d.createElement(t);n.async=!0;n.src=e;s=d.getElementsByTagName(t)[0];s.parentNode.insertBefore(n,s);}(window,document,'script','//qzonestyle.gtimg.cn/qzone/biz/gdt/dmp/user-action/gdtevent.min.js');
    gdt('init','1201935759');
    gdt('track','');
</script>
<noscript>
    <img height="1" width="1" style="display:none" src="https://a.gdt.qq.com/pixel?user_action_set_id=1201935759&action_type=&noscript=1"/>
</noscript>
<script>
    window.AliyunCaptchaConfig = {
        // 必填，验证码示例所属地区，支持中国内地（cn）、新加坡（sgp）
        region: "cn",
        // 必填，身份标。开通阿里云验证码2.0后，您可以在控制台概览页面的实例基本信息卡片区域，获取身份标
        // 注意：请在阿里云控制台获取实际的身份标替换此处的示例值
        prefix: "8hecib", // 请修改为实际的身份标
    };
</script>
<!--阿里云验证码2.0 SDK-->
<script
        type="text/javascript"
        src="https://o.alicdn.com/captcha-frontend/aliyunCaptcha/AliyunCaptcha.js"
></script>
<c:if test="${key != null }">
    <jsp:include page="${key }.jsp" />
</c:if>
<%@ include file="trace_fh.jsp" %>