<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib uri="lhwLink"  prefix="lhw" %>
<script src="/webstatic/js/jquery-3.5.1.min.js"></script>
<div class="tab-content">
	<div class="property-hotel">
		<h4>酒店简介</h4>
		<div class="fixed hotel-intro">${hotel.hotelDesc }</div>
		
		<c:if test="${fn:length(hotel.feature)>0 }">
		<h4>设施服务</h4>	
		<div class="fixed"><c:forEach var="b" items="${hotel.feature}" >  <c:forEach var="f" items="${HotelFeatureCodeList}" >
		<c:if test="${b eq f.code }"><p>• ${f.name }</p></c:if></c:forEach></c:forEach>
		</div>
		</c:if>
		
		<c:if test="${fn:length(hotel.landmarks)>0 }">
		<h4>周边景点</h4>	
		<div class="fixed"><c:forEach var="b" items="${hotel.landmarks}" > 
			<p>
				<span title="${b.name }">• ${b.name }</span>
				<span title="${b.nameEn }">&nbsp;&nbsp;${b.nameEn }</span>
				<span>&nbsp;&nbsp;
				<c:if test="${b.distance !=null}">
				<fmt:formatNumber value="${b.distance }"  minFractionDigits="1" ></fmt:formatNumber>公里
				</c:if>
				</span>
			</p>
			</c:forEach>
		</div>
		</c:if>
		
		<c:if test="${fn:length(hotel.transports)>0 }">
		<h4>公共交通</h4>	
		<div class="fixed"><c:forEach var="b" items="${hotel.transports}" > 
			<p>
				<span title="${b.name }">• ${b.name }</span>
				<span title="${b.type }&nbsp;${b.nameEn }">&nbsp;&nbsp;${b.type }&nbsp;${b.nameEn }</span>
				<span>&nbsp;&nbsp;<c:if test="${b.distance !=null}">
				<fmt:formatNumber value="${b.distance }"  minFractionDigits="1" ></fmt:formatNumber>公里</c:if>
				</span>
			</p></c:forEach>
		</div>
		</c:if>
		
	</div>
</div>
	
<div class="tab-content map-pos" style="height: 528px;">
	<div id="mapContainer"  style="width: 960px;height: 528px"></div>
	<div class="map-di"></div>
	<div class="map-load">地图加载中，请耐心等待</div>
	<br/>
	<%--<img src="/webstatic/img/map-pic.jpg" alt="">--%>
</div>

<c:if test="${key==null || key==''}">

<c:choose>
<c:when test="${hotel.countryCode eq 'CN' }">
<script src="//webapi.amap.com/maps?v=1.3&key=6e2788a0ddc9243f2309ff14c9900162"></script>
<script type="text/javascript">
$(".map-load,.map-di").hide();
var infoWindow;
var gdCenter = new AMap.LngLat(${hotel.longitude },${hotel.latitude });
//初始化地图对象，加载地图
var map = new AMap.Map("mapContainer",{
	resizeEnable: true, 
	view:new AMap.View2D({
		center:gdCenter,
		zoom:13
	})
});
//地图中添加地图操作ToolBar插件
map.plugin(["AMap.ToolBar"],function() {		
	var toolBar = new AMap.ToolBar(); 
	map.addControl(toolBar);		
});

//实例化点标记
function addMarker(){
	map.clearMap();
	marker = new AMap.Marker({				  
		icon:"//webapi.amap.com/images/marker_sprite.png",
		position:gdCenter
	});
	marker.click =function(){
		openInfo();
	}
	marker.setMap(map);  //在地图上添加点
	
	AMap.event.addListener(marker,'click',function(){ 
		 infoWindow.open(map,marker.getPosition());	
   });	
}

//在指定位置打开信息窗体
function openInfo(){
	//构建信息窗体中显示的内容
	var info = []; 
	info.push("<div class=\"map-pop fixed\"><dl><dd>"); 
	info.push("<img src='${hotel.coverUrl }' alt='${hotel.hotelName }(${hotel.hotelNameEn})  www.lhw.cn'/></dd><dt>");  
	info.push("<h4> ${hotel.hotelName }</h4>");  
	info.push("<p> <em>${hotel.hotelNameEn } </em></p>");  
	info.push("<p>${hotel.address }</p>");  
	//info.push("<div class='icon-show fixed'><span class='icon-GEP' title='${hotel.cny_rate_min_100 }''></span>");  
	//info.push("<span class='icon-IAC' title='${hotel.rate_min_100 }'></span></div>");  
	info.push("<div class='icon-show fixed'><c:forEach var='i'  items='${hotel.iconFeature }'><c:forEach var='f' items='${HotelFeatureCodeList }'><c:if test='${i eq f.code }'><span class='icon-${i }' title='${f.name }'></span></c:if></c:forEach></c:forEach></div>");
	info.push("<div class='btn'> <p> CNY <span></span>起  </p> <a href='javascript:;' class='detailLinks'>查看详情</a></div></dt></dl></div>");  
	
//	info.push("<div style=\"padding:0px 0px 0px 4px;\"><b>高德软件</b>");  
//	info.push("电话 : 010-84107000   邮编 : 100102");  
//	info.push("地址 : 北京市望京阜通东大街方恒国际中心A座16层</div></div>");  
      
	infoWindow = new AMap.InfoWindow({  
		content:info.join(""), //使用默认信息窗体框样式，显示信息内容
		offset:new AMap.Pixel(5, -30)
	}); 	
	infoWindow.open(map, gdCenter);
}
addMarker();
openInfo();

$("#viewbymap").on("click",function(){
	var price = $('.mar-top').first().find("em").html();
	if(price==''||typeof(price) == "undefined"){
		$(".btn").find("p").html('');
	}else{
		$(".btn").find("span").html(price);
	}
	
});


//地图
$("body").on("click",".mapLinks",function(){
	var price = $('.mar-top').first().find("em").html();
	$(".btn").find("span").html(price);
	$(".property-tab li").removeClass("active").eq(2).addClass("active");
	$(".property-content .tab-content").hide().eq(2).show();
});

</script>
</c:when>
<c:otherwise>
<script src="https://gmaps.dragongap.cn/maps/api/js?key=AIzaSyAvAu1D-6wjlms4giwxxQPW4w2MOrE4wB0"></script> 
<script type="text/javascript">
function initialize(price) {
	   var myCenter=new google.maps.LatLng(${hotel.latitude},${hotel.longitude});
	   var mapOptions = {
		    zoom: 16,
		    center: myCenter,
		    mapTypeId: google.maps.MapTypeId.ROADMAP
	   }
	  var map = new google.maps.Map(document.getElementById("mapContainer"), mapOptions);
		
	  var marker=new google.maps.Marker({
		  position:myCenter
		  });
	 
	  marker.setMap(map);
	  
		var info = []; 
		info.push("<div class=\"map-pop fixed\"><dl><dd>"); 
		info.push("<img src='${hotel.coverUrl }'/></dd><dt>");  
		info.push("<h4> ${hotel.hotelName }</h4>");  
		info.push("<p> <em>${hotel.hotelNameEn } </em></p>");  
		info.push("<p>${hotel.address }</p>");  
	//	info.push("<div class='icon-show fixed'><span class='icon-GEP' title='${hotel.cny_rate_min_100 }''></span>");  
	//	info.push("<span class='icon-IAC' title='${hotel.rate_min_100 }'></span></div>");  
	    info.push("<div class='icon-show fixed'><c:forEach var='i'  items='${hotel.iconFeature }'><c:forEach var='f' items='${HotelFeatureCodeList }'><c:if test='${i eq f.code }'><span class='icon-${i }' title='${f.name }'></span></c:if></c:forEach></c:forEach></div>");
		if(price==''||typeof(price) == "undefined"){
			info.push("<div class='btn'><a href='javascript:;' class='detailLinks'>查看详情</a></div></dt></dl></div>");  
		}else{
			info.push("<div class='btn'> <p> CNY <span>"+price+"</span>起  </p> <a href='javascript:;' class='detailLinks'>查看详情</a></div></dt></dl></div>");
		}
		  
		
	  var infowindow = new google.maps.InfoWindow({
		  content:info.join("")
		  });
	  infowindow.open(map,marker);
	  
	  google.maps.event.addListener(marker,'click',function(){ 
		  infowindow.open(map,marker);
	   });
	}

function loadScript() {
	  var script = document.createElement("script");
	  script.type = "text/javascript";
	  script.src = "//maps.googleapis.com/maps/api/js?key=AIzaSyAB2qDUJl2SdcHKcVURoze1owLpm2Y3k30&sensor=true&callback=initialize";
	  document.body.appendChild(script);
}

	$("#viewbymap").on("click",function(){
		var price = $('.mar-top').first().find("em").html();
		initialize(price);
		
	});
	
	// 地图
	$("body").on("click",".mapLinks",function(){
		var price = $('.mar-top').first().find("em").html();
		initialize(price);
		$(".property-tab li").removeClass("active").eq(2).addClass("active");
		$(".property-content .tab-content").hide().eq(2).show();
	});
	
	setTimeout(function(){
		$(".map-load,.map-di").hide();
	},5000);
	
	//loadScript();
	//google.maps.event.addDomListener(window, 'load', initialize);
</script>

</c:otherwise>
</c:choose>

</c:if>
<%-- /pages/hotel_content.jsp --%>