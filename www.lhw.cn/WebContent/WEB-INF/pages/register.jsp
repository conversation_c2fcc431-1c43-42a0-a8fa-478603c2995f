<%--
  Created by IntelliJ IDEA.
  User: <PERSON>
  Date: 2021/7/25
  Time: 22:10
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="lhwLink"  prefix="lhw" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="/webstatic/css/master.css?20161101">
    <link rel="stylesheet" href="/webstatic/register/lib/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="/webstatic/register/css/register.css">
    <link rel="stylesheet" href="/webstatic/register/lib/verify/verify.css">
    <title>会员注册</title>
    <%@ include file="gtm.jsp" %>
</head>
<body>
<!-- header -->
<div id="header"  style="background: none; height:auto;">
    <jsp:include page="/header.html" />
    <jsp:include page="/searchbar.html" />
</div>
<!-- End header -->
<div class="main" id="app" v-cloak>
    <div class="register-banner">
        <img src="/webstatic/register/images/register-banner.png" alt="register banner"/>
    </div>
    <!-- End ad -->

    <div class="pub-w">
        <div class="lhw-from">
            <div class="lhw-from-register">
                <div class="lfr-info">
                    <div class="lfr-info-head">
                        <h4>会员信息</h4>
                        <p>已是会员？<a href="javascript:;" @click="login">登录</a></p>
                    </div>
                    <div>
                        <el-form status-icon label-width="80px" :rules="rules" :model="formData" ref="registerRef">
                            <el-form-item label="称谓" prop="prefix">
                                <el-select v-model="formData.prefix">
                                    <el-option label="请选择称谓" value=""></el-option>
                                    <el-option label="先生Mr." value="Mr."></el-option>
                                    <el-option label="女士Ms." value="Ms."></el-option>
                                    <el-option label="夫人Mrs." value="Mrs."></el-option>
                                </el-select>
                            </el-form-item>
                            <div class="two-div">
                                <el-form-item label="姓" prop="last-name">
                                    <div class="two-div-input"><el-input placeholder="请输入姓氏" v-model="formData.lastName"></el-input></div>
                                    <div class=""><el-input v-model="formData['last-name']" @blur="changeLastName(formData['last-name'] || formData.lastName)"></el-input></div>
                                </el-form-item>
                                <el-form-item label="名" prop="first-name">
                                    <div class="two-div-input"><el-input placeholder="请输入名字" v-model="formData.firstName"></el-input></div>
                                    <div class=""><el-input v-model="formData['first-name']" @blur="changeFirstName(formData['first-name'] || formData.firstName)"></el-input></div>
                                </el-form-item>

                            </div>

                            <el-form-item label="手机" prop="personal">
                                <div class="phone-div">
                                    <div class="prepend">
                                        <el-form-item prop="beforePhone">
                                            <el-input v-model="formData.beforePhone"></el-input>
                                        </el-form-item>
                                    </div>
                                    <div class="input">
                                        <el-form-item prop="personal">
                                            <el-input v-model="formData.personal"></el-input>
                                        </el-form-item>
                                    </div>
                                </div>
                            </el-form-item>
                            <el-form-item label="邮箱" prop="primary-email">
                                <el-input :suffix-icon="emailLoadingClass" placeholder="请输入邮箱" v-model="formData['primary-email']" @blur="checkEmailAsync"></el-input>
                                <div v-if="toLogin" class="emailt-to-login">该邮箱已与有效会员账号关联，或被过期、取消的账号占用，请点击<a @click="login">此处</a>直接登录或使用用其它邮箱进行注册</div>
                                <div v-if="!formData['primary-email']" class="emailt-to-login">邮箱可进行账号登录、密码找回，建议填写常用邮箱地址</div>
                            </el-form-item>
                            <el-form-item label="确认邮箱" prop="checkEmail">
                                <el-input placeholder="请再次输入邮箱" v-model="formData.checkEmail"></el-input>
                            </el-form-item>
                            <el-form-item label="登录密码" prop="password">
                                <el-input :show-password="true" placeholder="请输入登录密码" v-model="formData.password" type="password" autocomplete="off"></el-input>
                                <div class="emailt-to-login">至少8个字符，其中包含大写字母、小写字母、数字或者特殊字符</div>
                            </el-form-item>
                            <el-form-item label="国家/地区">
                                <el-select v-model="formData.addresses['country-code']">
                                    <el-option label="请选择国家/地区" value=""></el-option>
                                    <el-option v-for="(value,key) in countries" :key="key" :label="value" :value="key"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="城市">
                                    <div class="div-input" style="margin-bottom: 10px"><el-input placeholder="请输入所在城市" v-model="formData.inputCity"></el-input></div>
                                    <div class="div-input"><el-input v-model="formData['city']" @blur="changeCity(formData['city'] || formData.inputCity)"></el-input></div>
                            </el-form-item>
                            <el-form-item label="首选语言" prop="preferred-language">
                                <el-select v-model="formData['preferred-language']">
                                    <el-option label="请选择首选语言" value=""></el-option>
                                    <el-option v-for="item in preferredLanguage" :key="item.key" :label="item.text" :value="item.key"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="订阅" style="margin-bottom:0;">
                                <p style="margin-top:10px;line-height:20px;">是否愿意收到更多有关LHW产品和服务、营销和促销相关信息（通过包括但不限于短信、电子邮件、平信等方式接收）？本人可随时按照相关通讯中的退订程序退订通讯。
                                </p>
                                <el-radio-group v-model="formData.isSubscribe" class="custom-radio">
                                    <el-radio :label="true">愿意</el-radio>
                                    <el-radio :label="false">不愿意</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item prop="clause" style="margin-bottom:0;">
                                <el-checkbox v-model="formData.clause">本人已仔细阅读并同意：</el-checkbox>
                                <ul style="line-height: 1.5;list-style: disc;padding-left: 45px;margin-bottom: 20px;">
                                    <li style="list-style: disc;">本人已仔细阅读并同意<a target="_blank" href="https://www.lhw.cn/privacy-policy" class="link-a">《立鼎世酒店集团中国隐私政策》</a></li>
                                    <li style="list-style: disc;">本人已仔细阅读并同意LHW为本隐私政策所述目的并以本隐私政策所述方式处理本人的敏感个人信息，并将本人的个人信息跨境传输至中国境内外的相关接收方。</li>
                                </ul>
                            </el-form-item>
                            <el-form-item style="text-align: center;position:relative">
                                <div ref="code" class="validCode"></div>
                                <el-button :style="{'opacity':isClickBtn ? 1 : 0.5}" class="el-button-custom" @click="submitForm">立即注册会员</el-button>
                            </el-form-item>
                        </el-form>
                    </div>
                </div>
                <div class="lfr-des">
                    <div class="el-image">
                        <img src="/webstatic/register/images/register-logo.png"/>
                    </div>
                    <div class="lfr-des-text">
                        <p>会员专享礼遇</p>
                        <p class="icon-1">入住前客房升级权益</p>
                        <p class="icon-2">累积积分兑换免费入住</p>
                        <p class="icon-3">入住专享礼遇</p>
                        <p class="icon-4">会员专享特惠</p>
                    </div>
                    <a target="_blank" href="https://www.lhw.cn/terms-and-conditions#leadersclub" class="lfr-des-more">尊享贵宾会条款适用</a>
                </div>
            </div>
        </div>
    </div>
</div>
<jsp:include page="/footer.html" />
<!-- footer -->
<div id="footer"></div>
<!-- End footer -->
<script src="/webstatic/register/lib/vue/vue.js"></script>
<script src="/webstatic/register/lib/vue/polyfill.min.js"></script>
<script src="/webstatic/register/lib/element-ui/lib/index.js"></script>
<script src="/webstatic/register/lib/dict//pinyin_dict_notone.js"></script>
<script src="/webstatic/register/lib/dict/pinyinUtil.js"></script>
<script src="/webstatic/register/lib/distpicker/district_zh.js"></script>
<script src="/webstatic/register/lib/verify/verify.min.js"></script>
<script src="/webstatic/js/handlebars-v3.0.0.js"></script>
<script>
    var memberUrl="${memberUrl}";
    var country = {
        'CN':	'中国','HK':	'中国香港','MO':	'中国澳门','TW':	'中国台湾','AD':'安道尔共和国','AE':	'阿拉伯联合酋长国','AF':	'阿富汗','AG':	'安提瓜和巴布达','AI':	'安圭拉岛','AL':	'阿尔巴尼亚','AM':	'亚美尼亚','AO':	'安哥拉','AR':	'阿根廷','AT':	'奥地利','AU':	'澳大利亚','AZ':	'阿塞拜疆','BB':	'巴巴多斯','BD':	'孟加拉国','BE':	'比利时','BF':	'布基纳法索','BG':	'保加利亚','BH':	'巴林','BI':	'布隆迪','BJ':	'贝宁','BL':	'巴勒斯坦',
        'BM':	'百慕大群岛','BN':	'文莱','BO':	'玻利维亚','BR':	'巴西','BS':	'巴哈马','BW':	'博茨瓦纳','BY':	'白俄罗斯','BZ':	'伯利兹','CA':	'加拿大','CF':	'中非共和国','CG':	'刚果','CH':	'瑞士','CK':	'库克群岛','CL':	'智利','CM':	'喀麦隆','CO':	'哥伦比亚','CR':	'哥斯达黎加','CS':	'捷克','CU':	'古巴','CY':	'塞浦路斯','CZ':	'捷克','DE':	'德国','DJ':	'吉布提','DK':	'丹麦','DO':	'多米尼加共和国',
        'DZ':	'阿尔及利亚','EC':	'厄瓜多尔','EE':	'爱沙尼亚','EG':	'埃及','ES':	'西班牙','ET':	'埃塞俄比亚','FI':	'芬兰','FJ':	'斐济','FR':	'法国','GA':	'加蓬','GB':	'英国','GD':	'格林纳达','GE':	'格鲁吉亚','GF':	'法属圭亚那','GH':	'加纳','GI':	'直布罗陀','GM':	'冈比亚','GN':	'几内亚','GR':	'希腊','GT':	'危地马拉','GU':	'关岛','GY':	'圭亚那','HN':	'洪都拉斯',
        'HT':	'海地','HU':	'匈牙利','ID':	'印度尼西亚','IE':	'爱尔兰','IL':	'以色列','IN':	'印度','IQ':	'伊拉克','IR':	'伊朗','IS':	'冰岛','IT':	'意大利','JM':	'牙买加','JO':	'约旦','JP':	'日本','KE':	'肯尼亚','KG':	'吉尔吉斯坦','KH':	'柬埔寨','KP':	'朝鲜','KR':	'韩国','KT':	'科特迪瓦共和国','KW':	'科威特','KZ':	'哈萨克斯坦','LA':	'老挝','LB':	'黎巴嫩','LC':	'圣卢西亚','LI':	'列支敦士登','LK':	'斯里兰卡',
        'LR':	'利比里亚','LS':	'莱索托','LT':	'立陶宛','LU':	'卢森堡','LV':	'拉脱维亚','LY':	'利比亚','MA':	'摩洛哥','MC':	'摩纳哥','MD':	'摩尔多瓦','MG':	'马达加斯加','ML':	'马里','MM':	'缅甸','MN':	'蒙古','MS':	'蒙特塞拉特岛','MT':	'马耳他','MU':	'毛里求斯','MV':	'马尔代夫','MW':	'马拉维','MX':	'墨西哥','MY':	'马来西亚','MZ':	'莫桑比克','NA':	'纳米比亚','NE':	'尼日尔','NG':	'尼日利亚','NI':	'尼加拉瓜',
        'NL':	'荷兰','NO':	'挪威','NP':	'尼泊尔','NR':	'瑙鲁','NZ':	'新西兰','OM':	'阿曼','PA':	'巴拿马','PE':	'秘鲁','PF':	'法属玻利尼西亚','PG':	'巴布亚新几内亚','PH':	'菲律宾','PK':	'巴基斯坦','PL':	'波兰','PR':	'波多黎各','PT':	'葡萄牙','PY':	'巴拉圭','QA':	'卡塔尔','RO':	'罗马尼亚','RU':	'俄罗斯','SA':	'沙特阿拉伯','SB':	'所罗门群岛','SC':	'塞舌尔','SD':	'苏丹','SE':	'瑞典','SG':	'新加坡','SI':	'斯洛文尼亚','SK':	'斯洛伐克','SL':	'塞拉利昂',
        'SM':	'圣马力诺','SN':	'塞内加尔','SO':	'索马里','SR':	'苏里南','ST':	'圣多美和普林西比','SV':	'萨尔瓦多','SY':	'叙利亚','SZ':	'斯威士兰','TD':	'乍得','TG':	'多哥','TH':	'泰国','TJ':	'塔吉克斯坦','TM':	'土库曼斯坦','TN':	'突尼斯','TO':	'汤加','TR':	'土耳其','TT':	'特立尼达和多巴哥','TZ':	'坦桑尼亚','UA':	'乌克兰','UG':	'乌干达','US':	'美国','UY':	'乌拉圭','UZ':	'乌兹别克斯坦','VC':	'圣文森特岛','VE':	'委内瑞拉','VN':	'越南',
        'YE':	'也门','YU':	'南斯拉夫','ZA':	'南非','ZM':	'赞比亚','ZR':	'扎伊尔','ZW':	'津巴布韦'
    };
    new Vue({
        el: '#app',
        watch: {
            'formData.firstName': function(val) {
                this.$nextTick(function(){
                    this.changeFirstName(val)
                })
            },
            'formData.lastName': function(val) {
                this.$nextTick(function(){
                    this.changeLastName(val)
                })
            },
            'formData.inputCity': function(val) {
                console.log(val)
                this.$nextTick(function(){
                    this.changeCity(val)
                })
            }
        },
        computed: {
            emailLoadingClass: function() {
                return this.emailLoading?'el-icon-loading':''
            },
            isClickBtn: function() {
                var prefix = this.formData.prefix,
                    clause = this.formData.clause,
                    personal = this.formData.personal,
                    checkEmail = this.formData.checkEmail,
                    password = this.formData.password;

                return prefix && this.formData['first-name'] && this.formData['last-name']
                    && clause && personal && this.formData['primary-email']
                    && checkEmail && password && this.formData['preferred-language']
            }
        },
        data: function() {
            var emailReg = /[^@ \t\r\n]+@[^@ \t\r\n]+\.[^@ \t\r\n]+/;
            var phoneRge = /^1[3456789]\d{9}$/;
            var passwordRge = /^(?=.*?[A-Z])(?=.*?[a-z])((?=.*?[0-9])|(?=.*?[#?!@$ %^&*-])).{8,16}$/;
            var userName = /^[a-zA-Z\-\(\) ]{1,25}$/

            var self = this;
            var validateName = function(rule, value, callback) {
                if (!userName.test(value)) {
                    callback(new Error('输入格式有误，请重新输入'));
                } else {
                    callback()
                }
            }
            var validateEmail = function (rule, value, callback) {
                if (value === '') {
                    callback(new Error('请输入邮箱'));
                } else if(!emailReg.test(value)) {
                    callback(new Error('请输入正确邮箱格式'));
                } else if(self.toLogin) {
                    callback(new Error('此邮箱已经注册'));
                }else{
                    if (self.formData.checkEmail !== '') {
                        self.$refs.registerRef.validateField('checkEmail');
                    }
                    callback();
                }
            }
            var validateEmail2 = function(rule, value, callback) {
                if (value === '') {
                    callback(new Error('请再次输入邮箱'))
                } else if(value !== self.formData['primary-email']) {
                    callback(new Error('两次输入邮箱不一致！'))
                } else {
                    callback()
                }
            }
            var numberRge = /[0-9]{5,}/
            var validatePhone = function(rule, value, callback) {
                if (value === '') {
                    callback(new Error('请输入手机号'))
                } else if (!phoneRge.test(value) && self.formData.beforePhone === '+86') {
                    callback(new Error('请输入正确手机号格式'))
                } else if( !numberRge.test(value) && self.formData.beforePhone !== '+86') {
                    callback(new Error('请输入正确号码格式'))
                }else {
                    callback()
                }
            }
            var validateClause = function(rule, value, callback) {
                if (!value) {
                    callback(new Error('请勾选隐私条款'))
                } else {
                    callback()
                }
            }

            var validatePassword = function(rule, value, callback) {
                if (value === '') {
                    callback(new Error('请输入密码'))
                } else if (!passwordRge.test(value)){
                    callback(new Error('密码格式输入错误'))
                }else {
                    callback()
                }
            }
            return {
                emailLoading: false,
                countries: country,
                preferredLanguage:[
                    { key: 'CN', text: '简体中文' },
                    { key: 'EN', text: '英文' },
                    { key: 'JP', text: '日文' },
                    { key: 'KR', text: '韩文' },
                    { key: 'FR', text: '法文' },
                    { key: 'DE', text: '德文' },
                    { key: 'IT', text: '意大利文' },
                    { key: 'ES', text: '西班牙文' },
                    { key: 'RU', text: '俄文' },
                ],
                formData:{
                    "personal": '',
                    "checkEmail": "",
                    "primary-email": "",
                    "password": "",
                    "user-type": "Registered User",
                    "prefix": "",
                    "preferred-language": "CN",
                    "isSubscribe": true,
                    "beforePhone": '+86',
                    "clause": false,
                    "firstName": "",
                    "lastName": "",
                    "first-name": "",
                    "last-name": "",
                    "phone-numbers": [],
                    addresses: {
                        "country-code": "CN",
                        "address-type": "Home",
                        "address1": "",
                        "city": ""
                    }

                },
                rules: {
                    prefix: [
                        { required: true, message: '请选择称谓', trigger: 'change' }
                    ],
                    clause: [{ validator: validateClause, trigger: 'blur' }],
                    personal: [
                        { required: true, validator:validatePhone,  trigger: 'blur' },
                    ],
                    'preferred-language': [
                        { required: true, message: '请选择首选语言', trigger: 'change' }
                    ],
                    firstName: [{ min: 1, max: 5, message: '长度不小于 5 个字符', trigger: 'blur' }],
                    lastName: [{ min: 1, max: 25, message: '长度不大于 25 个字符', trigger: 'blur' }],
                    'first-name': [
                        {
                            required: true, message: '请输入姓氏', trigger: 'blur'},
                        { validator: validateName, trigger: 'blur' }],
                    'last-name': [{
                        required: true, message: '请输入名字', trigger: 'blur'
                    },{  validator: validateName, trigger: 'blur' }],
                    'primary-email': [
                        { required: true, validator:validateEmail,  trigger: 'blur'},
                    ],
                    checkEmail: [
                        { required: true, validator:validateEmail2,  trigger: 'blur' },
                    ],
                    password: [
                        { required: true, validator: validatePassword, trigger: 'blur' }
                    ],
                    'city': [{
                        required: true, message: '请输入城市', trigger: 'blur'}],
                    },
                toLogin: false
            }
        },
        methods: {
            login: function() {
                $('body,html').animate({scrollTop: 0},500);
                setTimeout(function(){
                    $("#login").trigger("click")
                },502)
            },
            changeFirstName: function(val) {
                val = pinyinUtil.getPinyin(val,' ',false)
                val = val.slice(0,1).toUpperCase() + val.slice(1)
                this.formData['first-name'] = val
            },
            changeLastName: function(val) {
                val = pinyinUtil.getPinyin(val,' ',false)
                val = val.slice(0,1).toUpperCase() + val.slice(1)
                this.formData['last-name'] = val
            },
            changeCity: function(val) {
                val = pinyinUtil.getPinyin(val,' ',false)
                val = val.slice(0,1).toUpperCase() + val.slice(1)
                this.formData = {
                    ...this.formData,
                    city:val
                }
            },
            submitForm: function() {
                var self = this;
                this.$refs.registerRef.validate(function(valid){
                    if (valid) {
                        console.log("数据提交")
                        self.showValid()
                    } else {
                        console.log("error")
                        return false
                    }
                })
            },
            submitData: function() {
                var self = this;
                var data = Object.assign({},this.formData)
                data['phone-numbers'].push({
                    "phone-number-type": "Mobile",
                    "phone-number": data.beforePhone + data.personal
                })
                data.addresses['city'] = data.city
                data.addresses = [data.addresses]
                data.password = window.btoa(data.password)
                delete data.firstName
                delete data.lastName
                delete data.checkEmail
                delete data.beforePhone
                delete data.personal
                delete data.city

                dataLayer.push({
                    "event":"uaevent",
                    "eventCategory":'Register',
                    "eventAction":'Click_Register Info',
                    "eventLabel":'Click_Register Info',
                });
                let hotel_order_data = sessionStorage.getItem("hotel_order_data") || false;
                $.ajax({
                    type: 'POST',
                    url: memberUrl + '/register',
                    header: {
                        "Content-Type":"application/x-www-form-urlencoded"
                    },
                    data: {
                        infoString: JSON.stringify(data),
                    },
                    dataType: 'json',
                    beforeSend: function() {
                        self.submitLoading = true
                    },
                    success: function (res) {
                        if (res.code == '201') {
                            var date = new Date();
                            date.setTime(date.getTime()+7*24*60*60*1000);
                            $.cookie(uuid_cookie, res.ObjectData.membership.uuid, {expires:date, path:'/', domain:'lhw.cn'});
                            $.cookie(islogin_cookie, 1, {expires:date, path:'/', domain:'lhw.cn'});
                            $.cookie(member_lcnumber_cookie, res.ObjectData.membership.membershipnumber, {expires:date, path:'/', domain:'lhw.cn'});
                            $.cookie(identify_cookie, res.ObjectData.membership.email, {expires:date, path:'/', domain:'lhw.cn'});
                            if(hotel_order_data){
                                self.hotel_order_data_form(hotel_order_data)
                            }else{
                                window.location.href = '/register/success.html'//跳转成功页面
                            }
                            
                        } else {
                            ELEMENT.Message.error('注册失败！')
                        }
                    },
                    error: function() {
                        ELEMENT.Message.error('网络出错，请稍后再试！')
                    },
                    complete: function() {
                        self.submitLoading = false
                        if ($(self.$refs.code).children().length > 0) {
                            $(self.$refs.code).html('')
                        }
                    }
                })
            },
            hotel_order_data_form(formData){
                if(formData){
                    let msg = decodeURIComponent(formData);
                    let formList = msg.split("&");
                    let $form = $("<form method='post' action='//booking.lhw.cn/web/form'>").hide();
                    for (let i = 0; i < formList.length; i++) {
                        let item = formList[i].split('=');
                        if(item && item.length !== 0){
                            $form.append($("<input type='hidden' name='"+item[0]+"' value='"+item[1]+"'>"));
                        }
                    }
                    $form.appendTo($("body"));
                    $form.submit();
                }
            },
            checkEmailAsync: function() {
                var self = this;
                var email = this.formData['primary-email']
                if (!email) {
                    return
                }
                $.ajax({
                    type: 'GET',
                    url: memberUrl + "/checkRegisterEmail",
                    beforeSend: function() {
                        self.emailLoading = true
                    },
                    data: {
                        email: email
                    },
                    dataType: 'json',
                    success: function(res) {
                        console.log(res)
                        if (res['result-count']) {
                            self.toLogin = true
                        }else{
                            self.toLogin = false
                        }
                    },
                    error: function() {
                        $(self.$refs.code).html('')
                        ELEMENT.Message.error('网络出错，请稍后再试！')
                    },
                    complete: function() {
                        self.emailLoading = false
                    }
                })
            },
            showValid: function() {
                var self = this
                if ($(this.$refs.code).children().length > 0) {
                    $(this.$refs.code).html('')
                }
                $(this.$refs.code).slideVerify({
                    type : 2,		//类型
                    vOffset : 5,	//误差量，根据需求自行调整
                    vSpace : 5,	//间隔
                    imgUrl: '/webstatic/register/images/',
                    imgName : ['valid-1.png','valid-2.png','valid-3.png'],
                    imgSize : {
                        width: '400px',
                        height: '200px',
                    },
                    blockSize : {
                        width: '40px',
                        height: '40px',
                    },
                    barSize : {
                        width : '400px',
                        height : '40px',
                    },
                    ready : function() {
                    },
                    success : function() {
                        self.submitData()
                    },
                    error : function() {
                        //		        	alert('验证失败！');
                    }

                });
            }
        }
    })
</script>
</body>
</html>