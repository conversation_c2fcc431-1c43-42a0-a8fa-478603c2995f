<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="lhwLink"  prefix="lhw" %>
<!-- headNav -->
<div class="head-nav">
	<div class="pub-w pad-lf">
		<!-- 		<div class="head-share">
                    <a href="javascript:void(0);" class="link1 js-erwm"></a>
                    <a href="http://weibo.com/theLeadingHotels" target="_blank" class="link2"></a>
                    <a href="http://i.youku.com/u/UMTMwNzcwMTU0OA==" target="_blank" class="link3"></a>
                    <a href="https://www.facebook.com/LeadingHotels" target="_blank"  class="link4"></a>
                    <a href="https://twitter.com/LeadingHotels" target="_blank" class="link5"></a>
                    <a href="http://instagram.com/leadinghotelsoftheworld" target="_blank"  class="link6"></a>
                    <p class="pos-erwm js-show"><img src="/webstatic/img/erwm.jpg" /></p>
                    <p class="pos-erwm js-show"><img src="/webstatic/img/erwm3.jpg" /></p>
                </div> -->
		<div class="head-tx">
			<div class="head-share" style="margin-right:-6px;">
				<a href="javascript:void(0);" class="link1 js-erwm"></a>
				<p class="pos-erwm js-show"><img src="/webstatic/img/erwm3.jpg" /></p>
			</div>
			<div class="head-tel">
				<span class="tel-icon"></span>
				客服 400-1324-582
			</div>
			<div class="head-language"><span class="zh"><a href="javascript:;">中文</a></span> <i class="select-js-l"></i>
				<div class="select-main-d">
					<p class="zh"><a href="http://www.lhw.cn/">中文</a></p>
					<p class="en"><a href="http://www.lhw.com/">English</a></p>
					<p class="rb"><a href="http://jp.lhw.com/">日本语</a></p>
					<p class="deu"><a href="http://de.lhw.com/">Deutsch</a></p>
					<p class="ita"><a href="http://it.lhw.com/">Italiano</a></p>
					<p class="esp"><a href="http://es.lhw.com/">Espanol</a></p>
				</div>
			</div>
			<div class="head-mu" argument="1">
				<span class="sp-icon"></span>
				订单查询
				<div class="order-main">
					<form id="SearchOrderform" action="/web/detail" method="post">
						<input type="hidden" name="no" value="">
						<input type="hidden" name="name" value="">
					</form>
					<h4>输入您的订单号及姓氏进行查询</h4>
					<p><span>订单号</span> <input id="order_no" name="no" type="text"  required="required" autocomplete="off"/></p>
					<p><span>姓&nbsp;&nbsp;&nbsp;氏</span> <input id="order_name" name="name"  type="text"  required="required" autocomplete="off"/></p>
					<p class="error"><em>订单号和姓氏不能为空</em></p>
					<h6>未查询到订单，请确认以上信息</h6>
					<p class="rg"><button onclick="javascript:getOrder();">查询</button></p>
				</div>
			</div>
			<!-- 			<div class="head-sp"><span class="sp-icon"></div> -->
			<input type="hidden" name="isLogind" id="isLogind" value=""/>
			<div id="logining" class="logining">
				<div class="header-group registry-button-div">
					<button id="registry" type="button">注册</button>
				</div>
				<div class="header-group login-button-div">
					<button id="login" type="button">登录</button>
				</div>
			</div>
			<div id="logined" class="logined">
				<div class="head-logout" style="display: none;">
					<a class="logout-btn" href="javascript: void(0);">
						<span class="logout-icon"></span>
						退出
					</a>
				</div>
				<div class="header-group clock" onclick="javascript:showModalWinPublicNum();">
					<label></label>
				</div>
				<div class="header-group" id="memberRoomUpgrad" title="可使用客房升级次数">
					<div class="room-opgraded">
						<label></label>
						<div class="room-opgraded-number">
							<p id="roomUpgraded"></p>
						</div>
					</div>
				</div>
				<div class="header-group">
					<p class="rg">
						<span id="score_is"></span><span id="memberScore"></span>
					</p>
				</div>
				<div class="header-group">
					<p class="rg">
						<span id="hello"></span><span class="member-name" id="memberName"></span>
					</p>
				</div>
				<div class="header-group level" id="levelC">
					<!-- 					<img src="/webstatic/img/c2x.png"/> -->
				</div>
				<div class="header-group level" id="levelS">
					<!-- 					<img src="/webstatic/img/s2x.png"/> -->
				</div>
				<div class="header-group level" id="levelA">
					<!-- 					<img src="/webstatic/img/a2x.png"/> -->
				</div>
			</div>
			<%-- 			</c:if> --%>
		</div>
		<div class="login-form">
			<div class="inner-box">
				<div class="desc">您已选择尊享贵宾会会员专享特惠价</div>
				<div class="header"><p>请登录尊享贵宾会账户</p></div>
				<div class="body">
					<div class="form">
						<div class="name">
							<label><img src="/webstatic/img/email_1.png"/></label>
							<input tabindex="1" id="email" name="email" maxlength="50" type="email" autocomplete="new-password" placeholder="注册会籍时使用的邮箱">
							<label class="name-warn"><img src="/webstatic/img/warn_1.png"/></label>
							<p id="email-error">请输入正确的邮箱</p>
							<p id="email-empty">请输入您的邮箱</p>
						</div>
						<div class="name-error-msg"><span></span></div>
						<div class="value">
							<label><img src="/webstatic/img/pwd_1.png"/></label>
							<input tabindex="2" id="password" name="password" type="password" autocomplete="new-password" placeholder="区分大小写">
							<label class="value-warn"><img src="/webstatic/img/warn_1.png"/></label>
							<p id="password-error">请输入正确的密码</p>
							<p id="password-empty">请输入您的密码</p>
						</div>
						<div class="value-error-msg"><span></span></div>
						<div class="forgot-pwd"><a onclick="javascript:showforgetPwdWin();">忘记密码</a></div>
						<div class="btn-submit">
							<button tabindex="3" id="button" onclick="javascript:memberLogin();">登录</button>
						</div>
						<div class="weixin-login">
							<img src="/webstatic/img/wechat_1.png"/>
							<a class="a-login" onclick="javascript:registryw();">微信登录</a>
						</div>
					</div>
				</div>
				<!--
			 <div class="footer">
					<div class="forgot-pwd" onclick="javascript:showforgetPwdWin();"><a>忘记密码</a></div>
					<div class="registry" onclick="javascript:registry();"><a>注册</a></div>
					<div class="clear"></div>
				</div>
				-->
			</div>

			<div class="inner-box-right">
				<div class="right_title">免费加入尊享贵宾会</div>
				<div class="right_desc">入住全球四百家卓尔不凡的成员酒店均可专享尊贵礼遇</div>
				<a class="toregister" href="/leaders-club/benefits-booking">立即加入尊享贵宾会</a>
			</div>
			<div class="close-icon"><img id="close_login" alt="close" src="/webstatic/img/close_1.png"></div>

		</div>

		<!--
		<div class="login-form">
			<div class="inner-box">
				<div class="header"><p>尊敬的尊享贵宾会会员</p></div>
				<div class="body">
					<p>由于系统维护，尊享贵宾会会员登录功能暂时无法使用。如有任何需要协助，请发送邮件至<b><EMAIL></b> 或拨打<b>400-1324-582</b>，由旅行顾问为您服务。非常抱歉由此带来的不便。</p>
				</div>
			</div>
			<div class="close-icon"><img id="close_login" alt="close" src="/webstatic/img/close_1.png"></div>

		</div>
		-->
		<div class="forget-pwd">
			<div class="inner-box">
				<div class="close-icon" onclick="javascript:closeForgetPwdWin();"><img alt="close" src="/webstatic/img/close_1.png"></div>
				<div class="header"><p>忘记密码</p></div>
				<div class="content">
					<span>输入您的电子邮件并点击重置密码，我们将向您发送电子邮件帮助您重置您的密码。</span>
				</div>
				<div class="email">
					<p>邮箱</p>
					<input tabindex="1" id="forget_email" name="forget_email" maxlength="32" type="email"
						   autocomplete="new-password" placeholder="注册会籍时使用的邮箱">
					<label class="value-warn" style="display: inline-block;"><img src="/webstatic/img/warn_1.png" style="vertical-align: -7px;" /></label>
					<p><span class='forget-pwd-error'></span></p>
				</div>
				<div class="btn-submit">
					<button onclick="javascript:resetPwd();">重置密码</button>
				</div>
			</div>
		</div>

		<div class="forget-pwd-success">
			<div class="inner-box">
				<div class="close-icon" onclick="javascript:closeForgetPwdSuccessWin();"><img alt="close" src="/webstatic/img/close_1.png"></div>
				<div class="header"><p>重置密码</p></div>
				<div class="content">
					<span>✔重置密码邮件已发送至注册邮箱，请尽快查收。</span>
				</div>
				<div class="btn-submit">
					<button onclick="javascript:closeForgetPwdSuccessWin();">关闭</button>
				</div>
			</div>
		</div>

		<div class="registry-form">
			<div class="inner-box">
				<div class="header"><p>会员注册</p></div>
				<div class="content">
					<span>请通过以下方式， 注册贵宾会籍</span><br>
					<span>贵宾服务热线<a class="phone" href="javascript:void(0);">400-1324-582</a></span><span>，或电邮 <a class="mail" href="mailto:<EMAIL>"><EMAIL></a></span>
				</div>
				<div class="btn-submit">
					<button onclick="javascript:toLogin();">立即登录</button>
				</div>
				<div class="close-icon" onclick="javascript:closeModalWinr();"><img alt="close" src="/webstatic/img/close_1.png"></div>
			</div>
		</div>

		<div class="m-registry-form">
			<div class="inner-box">
				<div class="header"><p>会员注册</p></div>
				<div class="content">
					请拨打我们的客服电话进行线下注册
				</div>
				<div class="phone">
					<a href="javascript:void(0);">400-1324-582</a>
				</div>
				<div class="close-icon" onclick="javascript:closeModalWinrm();"><img alt="close" src="/webstatic/img/close_1.png"></div>
			</div>
		</div>

		<div class="forgot-form">
			<div class="inner-box">
				<div class="header"><p>会员注册</p></div>
				<div class="content">
					请拨打我们的客服电话进行线下注册
				</div>
				<div class="phone">
					<a href="javascript:void(0);">400-1324-582</a>
				</div>
				<div class="content2"><a href="javascript: noMemberbook('forgot-form');">非会员直接预定></a></div>
				<div class="close-icon" onclick="javascript:closeModalWinp();"><img alt="close" src="/webstatic/img/close_1.png"></div>
			</div>
		</div>

		<div class="public-num-form">
			<div class="inner-box">
				<!-- 				<div class="header" id="noPromotionPub"> -->
				<!-- 					立鼎世公众号 -->
				<!-- 				</div> -->
				<div class="erwm">
					<img src="/webstatic/img/erwm3.jpg" width="120px" height="120px">
				</div>
				<div class="tips" id="promotionTips">
					<p>您可使用立鼎世公众号完成更多会员操作</p>
				</div>
				<div class="notips" id="noPromotionTips">
					<p>您可使用立鼎世公众号完成更多会员操作</p>
				</div>
				<div class="midder-button-content">
					<p id="public_promotion_desc">
						尊敬的会员，立鼎世邀请您参加我们新年的促销活动，凡在6月份预订巴黎酒店的会员可享双倍积分。</p>
				</div>
				<div class="midder-button-more">
					<p class="midder-text" onclick="javascript: seemore();">查看更多</p>
				</div>
				<div class="close-icon" onclick="javascript:closeModalWinPublicNum();"><img alt="close" src="/webstatic/img/close_1.png"></div>
			</div>
		</div>

		<div class="qrcode-login-form">
			<div class="inner-box">
				<div class="header">
					<p>请打开微信<br/>扫描二维码登录</p>
				</div>
				<div class="qrcode-login-cl" id="qrcode-login-id">
				</div>
				<div><img id='imgOne'  src="" style='margin-top:40px'/></div>
				<div class="close-icon" onclick="javascript:closeQrcodeLoginWinw();"><img alt="close" src="/webstatic/img/close_1.png"></div>
			</div>
		</div>

		<div class="weichat-form">
			<div class="inner-box">
				<div class="erwm">
					<img src="/webstatic/img/erwm3.jpg" width="120px" height="120px">
				</div>
				<div class="footer">
					<p>您可关注立鼎世公众号，进行绑定，并管理账号</p>
				</div>
				<div class="close-icon" onclick="javascript:closeModalWinw();"><img alt="close" src="/webstatic/img/close_1.png"></div>
			</div>
		</div>

	</div>

</div>
<!-- End headNav -->

<script type="text/javascript">
	function getOrder() {
		var no = $("#order_no").val();
		no = $.trim(no) ;
		var name = $("#order_name").val();
		name = $.trim(name) ;
		if(no == ''){
			$(".error").find("em").html("请输入订单号！");
			$(".error").show();
			return;
		}
		if (name=='' ){
			$(".error").find("em").html("请输入入住人姓氏！");
			$(".error").show();
			return;
		}else{
			$(".rg button").text("正在查询...");
		}
		dataLayer.push({
			"event":"uaevent",
			"eventCategory":"Public",
			"eventAction":"Click_Top",
			"eventLabel":"Order query",
		});


		//检测 订单号和姓氏不能为空
		//为空 显示 订单号和姓氏不能为空
		var url;
		if (window.location.protocol ==="http:"){
			url = "//www.lhw.cn/web/detail";
		}else{
			url  = "/web/detail";
		}

		$.get(url, {
			"no" : no,
			"name" : name
		}, function(data) {
			if (data=="ERROR"){
				// alert("ERROR");// 显示 订单号和姓氏不能为空
				//$(".order-main h6").hide();
				$(".error").find("em").html("订单号和姓氏不能为空");
				$(".error").show();
				$(".rg button").text("查询");
			}else if (data=="OK"){
				$(".error").find("em").html("");
				$("#SearchOrderform input[name=no]").val(no);
				$("#SearchOrderform input[name=name]").val(name);
				$("#SearchOrderform").submit();
			}else if (data=="NOT FOUND"){
				// alert("NOT FOUND"); // 显示 未查询到订单，请确认以上信息
				$(".error").find("em").html("未查询到订单，请确认以上信息");
				$(".error").show();
				$(".rg button").text("查询");

			}
		});
	}

</script>

<!-- menu -->
<div class="menu">
	<div class="pub-w pad-lf">
		<div class="menu-l">
			<a href="//www.lhw.cn/index.html" class="logo" id="un_member_logo" style="display:none"></a>
			<a href="//www.lhw.cn/index.html" class="member-logo" id="member_logo"  style="display:none"></a>
		</div>
		<div class="menu-b">
			<ul>
				<li><a onclick="sendDataLayerHeader('Domestic Hotel')" href="//www.lhw.cn/domestic">国内酒店</a></li>
				<li><a onclick="sendDataLayerHeader('International Hotel')" href="//www.lhw.cn/international">海外酒店</a></li>
				<li class="select-js-jrth 	"><a onclick="sendDataLayerHeader('leaders-club')" href="//www.lhw.cn/leaders-club/">尊享贵宾会</a>
					<div class="select-main-jrth hover-div">
						<p><a onclick="sendDataLayerHeader('leaders-club::About leaders-club')" href="//www.lhw.cn/leaders-club/">关于尊享贵宾会</a></p>
						<p><a onclick="sendDataLayerHeader('leaders-club::Exclusive-benefits')" href="//www.lhw.cn/leaders-club/benefits">专享礼遇</a></p>
						<p><a href="//www.lhw.cn/leaders-club/manage-points">积分获取</a></p>
						<p><a onclick="sendDataLayerHeader('leaders-club::Benefits-compare')" href="//www.lhw.cn/leaders-club/benefits-compare">会员权益</a></p>
						<p><a onclick="sendDataLayerHeader('leaders-club::User Guide')" href="//www.lhw.cn/leaders-club/how-it-works">使用指南</a></p>
					</div>
				</li>
				<!-- 					<li class="select-js-jrth"><a href="javascript:void(0)">约惠立鼎世</a> -->
				<!-- 						<div class="select-main-jrth"> -->
				<!-- 							<p><a href="http://www.lhw.cn/offers/Ritz-Paris">巴黎丽兹开幕优惠</a></p>-->
				<!-- 							<p><a href="http://www.lhw.cn/offers/members-only-sale">会员专享5折优惠</a></p> -->
				<!--  							<p style="display:none;"><a href="http://www.lhw.cn/leaders-club">赠送贵宾会会籍</a></p> -->
				<!--  							<p><a href="http://www.lhw.cn/offers/Ritz-London">2017伦敦丽兹社交盛会</a></p> -->
				<!--  							<p><a href="http://www.lhw.cn/offers/apr18-members-only-sale">会员专享限时优惠</a></p> -->
				<!-- 							<p><a href="http://www.lhw.cn/offers/2018-Chinese-New-Year">中国独家新年礼遇</a></p> -->
				<!-- 							<p><a href="http://www.lhw.cn/offers/The-Legian-Bali">巴厘岛乐吉安度假酒店春节特惠</a></p> -->
				<!-- 							<p><a href="http://www.lhw.cn/offers/1212sale">双十二特惠</a></p> -->
				<!-- 							<p><a href="http://www.lhw.cn/offers/apacmembers4for3">住4付3特惠</a></p> -->
				<!-- 							<p><a href="http://www.lhw.cn/offers/The-Datai-Langkawi">兰卡威独特住宿体验</a></p> -->
				<!-- 						</div> -->
				<!-- 					</li>	 -->
				<li class="select-js-l"><a onclick="sendDataLayerHeader('Explore')" href="javascript:void(0)">灵感探索</a>
					<div class="select-main hover-div">
						<p><a onclick="sendDataLayerHeader('Explore::Destination-experiences')" href="//www.lhw.cn/offers/destination-experiences">非凡体验</a></p>
						<p><a onclick="sendDataLayerHeader('Explore::Special-offer')" href="//www.lhw.cn/offers/rate-plans">优享特惠</a></p>
						<p><a onclick="sendDataLayerHeader('Explore::Hotel Features')" href="//www.lhw.cn/offers/collections">酒店特色</a></p>
						<p><a onclick="sendDataLayerHeader('Explore::New-hotels')" href="//www.lhw.cn/offers/new-hotels">最新成员</a></p>
						<p><a onclick="sendDataLayerHeader('Explore::Magnificent-journeys')" href="//www.lhw.cn/magnificent-journeys">璀璨之旅</a></p>
						<p><a onclick="sendDataLayerHeader('Explore::Brand-story')" href="//www.lhw.cn/brand-story/overview">品牌介绍</a></p>
						<p><a onclick="sendDataLayerHeader('Explore::Brand-history')" href="//www.lhw.cn/brand-story/history">发展历程</a></p>
					</div>
				</li>
			</ul>
		</div>
	</div>
</div>
<!-- End menu -->

<div class="page-mask"></div>
<div class="loading-mask">
	<img src="/webstatic/img/small_loader.gif" alt="gif">
</div>

<div class="promotion-box">
	<div class="promotion-form">
		<div class="inner-box">
			<div class="promotion-img">
				<img id="promotionImg" src="/webstatic/img/promotion.png">
			</div>
			<div class="header"><p id="actname">新年促销活动</p></div>
			<div class="midder-content">
				<p id="actDesc" class="text-left">尊敬的会员，立鼎世邀请您参加我们新年的促销活动，凡在6月份预订巴黎酒店的会员可享双倍积分。</p>
			</div>
			<div class="midder-button-partaking">
				<p class="midder-text" onclick="javascript:partakingActivity();">参与活动</p>
			</div>
			<div class="midder-button-partaked"> <!-- onclick="javascript:partakedActivity();" -->
				<p class="midder-text">已参与</p>
			</div>
			<div class="midder-button-isee" onclick="javascript:iseeActivity();">
				<p class="midder-text">我知道了</p>
			</div>
			<div class="promotionTip"><p>*点击即表示您愿意参与此活动</p></div>
			<input type="hidden" name="actId" id="actId">
			<div class="close-icon" onclick="javascript:closeModalWinPromotion();"><img alt="close" src="/webstatic/img/close_4.png"></div>
		</div>
	</div>
	<%-- 	<input type="hidden" id="showPromotion" name="${suser.showPromotion}"> --%>
</div>

<div class="renew-form">
	<div class="inner-box">
		<!--<div class="header" id="expired">
			<p>抱歉，您的会籍已于</br><span id="expirationdate"></span>到期，请及时拨打</p>
		</div> -->
		<div class="header" id="expired">
			<p>
				您的会籍已到期，尊享贵宾会计划现已免费！诚邀重新加入会籍，尊享丰富会员权益！
			</p>
		</div>
		<div class="header" id="cancelled">
			<p>您的会籍已失效，尊享贵宾会计划现已免费！诚邀重新加入会籍，尊享丰富会员权益！</p>
		</div>

		<div class="btn-submit">
			<button tabindex="3" onclick="javascript:registry();">重新注册</button>
		</div>
		<div class="close-icon" onclick="javascript:closeRenewWinw();"><img alt="close" src="/webstatic/img/close_1.png"></div>
	</div>
</div>
<div id="captcha-element" style="display: none;"></div>
<!--预留的验证码元素，用于配置初始化函数中的element参数-->
<!--3.新建一个<script>标签，用于调用验证码初始化函数initAliyunCaptcha-->
<script type="text/javascript">
	var captcha;
	// 弹出式，除region和prefix以外的参数
	window.initAliyunCaptcha({
		// 场景ID。根据步骤二新建验证场景后，您可以在验证码场景列表，获取该场景的场景ID
		SceneId: "8hnztawd",
		// 验证码模式，popup表示弹出式，embed表示嵌入式。无需修改
		mode: "popup",
		// 页面上预留的渲染验证码的元素，与原代码中预留的页面元素保持一致。
		element: "#captcha-element",
		// 触发验证码弹窗或无痕验证的元素
		button: "#button",
		// 验证码验证通过回调函数
		success: function (captchaVerifyParam) {
			console.log('验证码验证成功，参数:', captchaVerifyParam);
			// 保存验证参数到全局变量
			window.captchaVerifyParam = captchaVerifyParam;
			window.captchaSceneId = "8hnztawd";

			// 触发登录流程
			performLogin();
		},
		// 验证码验证不通过回调函数
		fail: function (result) {
			// 入参为不通过信息
			// 正常验证有效期内不需要做任何操作，验证码自动刷新，重新进行验证
			console.error(result);
		},
		// 绑定验证码实例回调函数，该回调会在验证码初始化成功后调用
		getInstance: function (instance) {
			captcha = instance;
		},
		// 滑块验证和一点即过的验证形态触发框体样式，支持自定义宽度和高度，单位为px。
		slideStyle: {
			width: 360,
			height: 40,
		},
		// ...其他参数，参考initAliyunCaptcha参数说明
	});
</script>
<!-- 浮动banner -->
<!--<div class="fixed-banner"  id="float-banner" style="display:none;z-index:999;">
<div class="fixed-banner-inner">
<div class="openbtn" style=" background: url('/webstatic/img/btn_bg.png') center 26px no-repeat;  background-size:100% auto;">
<img src="/webstatic/img/openbtn.png" alt="" class="animated iteration-count-3 bounce">
<img src="/webstatic/img/left.png" class="tip" alt="">
</div>
<div class="banner-box">
<button class="closebtn" style="background:url('/webstatic/img/closebtn.png') 0 0 no-repeat; background-size:100% 100%;"></button>
<a href="http://visitleading.lhw.cn/?utm_source=banner-expand&utm_medium=main-site&utm_content=visitleading&utm_campaign=visitleading&utm_term=" target="_blank"><img src="/webstatic/img/confirmationBanner_700x180-1.jpg" alt=""></a>
</div>
</div>
</div>-->

<script src="/webstatic/js/jquery-3.5.1.min.js"></script>
<script src="/webstatic/js/cookie.js"></script>
<script src="/webstatic/js/jquery.cookie.js"></script>
<script src="/webstatic/js/handlebars-v3.0.0.js"></script>
<script src="/webstatic/js/common.js?20160322"></script>
<script>

	var floatBannerClosed = getCookie("floatBannerClosed");//记录浮动banner是否已经关闭了
	var isFirstPage = getCookie("isFirstPage");//记录当前页是否是用户进来的第一个页面
	if(!floatBannerClosed){
		floatBannerClosed = false;
	}
	if(!isFirstPage){
		isFirstPage = "true";
		setCookie("isFirstPage","false","s3600");
	}
	console.log("isFirstPage: "+isFirstPage);
	//	console.log("serverName: ${pageContext.request.serverName}  basePath:${pageScope.basePath} contextPath: ${pageContext.request.contextPath} test： ${fn:indexOf(pageContext.request.serverName,'booking.lhw.cn')} test：${fn:indexOf(pageContext.request.serverName,'booking.lhw.cn')<0}");
	$(document).ready(function(){
		$(".closebtn").click(function(event) {
			$(this).parent('.banner-box').hide();
			$(this).parent('.banner-box').siblings('.openbtn').show();
			setCookie("floatBannerClosed",true,"s3600");
		});
		$('.openbtn').click(function(){
			$(this).hide();
			$(this).siblings('.banner-box').show();
			setCookie("floatBannerClosed",false,"s3600");
		});

		var url = window.location.href;
		console.log(url.indexOf("booking.lhw.cn")+"  "+url);

		if(url.indexOf("booking.lhw.cn")<=0){
			$("#float-banner").show();
			console.log("floatBannerClosed: "+getCookie("floatBannerClosed"));
			if(getCookie("floatBannerClosed")=="true"||isFirstPage=="false"){
				$(".closebtn").click();
				setCookie("floatBannerClosed",true,"s3600");
			}
		}
	});
</script>

<script type="text/javascript">
	function hotel_order_data_form(formData){
		if(formData){
			let msg = decodeURIComponent(formData);
			let formList = msg.split("&");
			let $form = $("<form method='post' action='//booking.lhw.cn/web/form'>").hide();
			for (let i = 0; i < formList.length; i++) {
				let item = formList[i].split('=');
				if(item && item.length !== 0){
					$form.append($("<input type='hidden' name='"+item[0]+"' value='"+item[1]+"'>"));
				}
			}
			$form.appendTo($("body"));
			$form.submit();
		}
	}

	var islogin_cookie = "lhw.cn_islogin";
	var identify_cookie = "lhw.cn_identify";
	var key_code_cookie = "lhw.cn_key_code";
	var member_level_cookie = "lhw.cn_member_level";
	var member_score_cookie = "lhw.cn_member_score";
	var member_lcnumber_cookie = "lhw.cn_member_lcnumber";
	var first_name_cookie = "lhw.cn_first_name";
	var last_name_cookie = "lhw.cn_last_name";
	var prefix_cookie = "lhw.cn_prefix";
	var phone_cookie = "lhw.cn_phone";
	var uuid_cookie = "lhw.cn_uuid";
	var roomUpgraded_cookie = "lhw.cn_roomUpgraded";
	var isopen_cookie = "lhw.cn_isopen";
	var renew_cookie = "lhw.cn_renew";
	var expirationdate_cookie = "lhw.cn_expirationdate";
	var date=new Date();
	date.setTime(date.getTime()+7*24*60*60*1000); //设置date为当前时间一周


	function sendDataLayerHeader(menu){
		dataLayer.push({
			"event":"uaevent",
			"eventCategory":"Public",
			"eventAction":"Click_HeaderNav",
			"eventLabel":menu,
		});
	}


	function showModalWin(bool) {
// 	alert(123123);
		if(bool){
			$('.login-form .desc').css('opacity',1);
		}else{
			$('.login-form .desc').css('opacity',0);
		}
		$(".page-mask").show();
		$(".login-form").show();
		$("body").addClass("unscroll");
	}
	function closeModalWin() {
		$(".login-form").hide();
		var $errorName = $('.login-form .name-error-msg span');
		var $errorValue = $('.login-form .value-error-msg span');
		$(".login-form").hide();
		$(".page-mask").hide();
		$(".form input").val("");
		$errorName.text("") && $errorValue.text("");
		$(".login-form .name-warn").removeClass("visible");
		$(".login-form .value-warn").removeClass("visible");
		$(".form input").removeClass("invalidInput");
		sessionStorage.removeItem('hotel_order_data');
		$("body").removeClass("unscroll");
	}
	function showModalWinr() {
		$(".registry-form").show();
		$(".page-mask").show();
		$("body").addClass("unscroll");
	}
	function closeModalWinr() {
		$(".registry-form").hide();
		$(".page-mask").hide();
		$("body").removeClass("unscroll");
	}
	function showModalWinrm() {
		$(".m-registry-form").show();
	}
	function closeModalWinrm() {
		$(".m-registry-form").hide();
	}
	function showModalWinw() {
		$(".weichat-form").show();
		closeModalWin();
	}

	function closeForgetPwdWin(){
		$(".forget-pwd").hide();
	}

	function showforgetPwdWin(){
		$(".forget-pwd").show();
		closeModalWin();
	}

	function closeForgetPwdSuccessWin(){
		$(".forget-pwd-success").hide();
	}

	function showForgetPwdSuccessWin(){
		$(".forget-pwd-success").show();
	}

	function closeQrcodeLoginWinw() {
		$(".qrcode-login-form").hide();
	}

	//发送密码重置邮件
	function resetPwd(){
		var email = $('.forget-pwd').find('input[name=forget_email]').val();
		var emailReg = /\w+((-w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+/;
		var $errorName = $('.forget-pwd-error');
		if (!email) {
			$errorName.text('请输入您的邮箱！');
			$('.name-error-img').show();
			$('.forget-pwd .email .value-warn').addClass("visible");
			$('.forget-pwd .email input').addClass("errorBorder");
			return false;
		}

		if (!emailReg.test(email)) {
			$errorName.text('邮箱格式错误，请重新输入！');
			$('.name-error-img').show();
			$('.forget-pwd .email .value-warn').addClass("visible");
			$('.forget-pwd .email input').addClass("errorBorder");
			return false;
		}
// 	clearContent($errorName);
		$errorName.text('');
		$('.forget-pwd .email .value-warn').removeClass("visible");
		$('.forget-pwd .email input').removeClass("errorBorder");
		// 提交表单
		$.ajax({
			async:true,
			cache:false,
			timeout:30000,
			url:"/forgetPassword",
			data:{
				"email" : email
			},
			error:function(jqXHR, textStatus, errorThrown){
				if(textStatus=="timeout"){
					alert("加载超时！请重试！");
				}else{
					alert('失败，请重试！');
				}
			},
			success:function(data) {
				if (data == 'success') {
					closeForgetPwdWin();
					$(".forget-pwd-success").show();
				} else if(data == 'check-false'){
					$errorName.text('您输入的用户不存在！');
					$('.name-error-img').show();
					$('.forget-pwd .email .value-warn').addClass("visible");
					$('.forget-pwd .email input').addClass("errorBorder");
					return false;
				} else{
					$errorName.text('失败，请重试！');
					$('.name-error-img').show();
					$('.forget-pwd .email .value-warn').addClass("visible");
					$('.forget-pwd .email input').addClass("errorBorder");
					return false;
				}
			}
// 		complete : function(XMLHttpRequest, status) {
// 			closeForgetPwdWin();
// 			$(".forget-pwd-success").show();
// 		}
		});


	}

	function showQrcodeLoginWinw() {
		var uuidId = new UUID();
		$('#qrcode-login-id').html("")
		var qrcode = $('#qrcode-login-id').qrcode({
			render: "canvas", // 渲染方式有table方式和canvas方式
			width: 192,   //默认宽度
			height: 192, //默认高度
			text: "https://open.weixin.qq.com/connect/oauth2/authorize?"+
					// 			    "appid=wx434ab13e50abf3f9&redirect_uri="+
					// 				"http://yzhs8c.natappfree.cc/lhw-member/qrcode/login?unionId="+uuidId+
					"appid=wx5ec0c2be82d29fc3&redirect_uri="+
					"http://member.lhw.cn/lhw-member/qrcode/login?unionId="+uuidId+
					"&response_type=code&scope=snsapi_userinfo&state=1#wechat_redirect"}).hide();

		var canvas=qrcode.find('canvas').get(0);
		$('#imgOne').attr('src',canvas.toDataURL('image/jpg'))
		$(".qrcode-login-form").show();
		var uuidIdStr = uuidId.id;
		var interval = setInterval(function(){
			// 提交表单
			$.ajax({
				type: "POST",
				url: "/qrlogin",
				data: {"uuidId" : uuidIdStr},
				timeout:20000,
				dataType : "json",
				success: function(data){
					if (data.isLogind == "1") {
						console.log("获取到扫码结果")
						clearInterval(interval);
						if(data.memberInfoResult.code == 'SUCCESS'){
//		 				window.location.assign("http://www.lhw.cn/index.html");
							var memberInfo = data.memberInfoResult;
							$("#score_is").text("积分：");
							$("#hello").text("你好,");
							$("#memberScore").text(scoreFmt(memberInfo.memberScore));
							$("#memberName").text(memberInfo.firstName).css('cursor','pointer').click(function(){
								location.href = '/member/info.html';
							});
							$("#roomUpgraded").text(memberInfo.roomUpgraded);
							$("#isLogind").val(data.isLogind);
							//设置登录追踪
							var dataLayer = window.dataLayer || [];
							var loginUuid = data.memberInfoResult.uuid
							console.log(loginUuid)
							if(loginUuid&&loginUuid!=""){
								dataLayer.push({
									'event' : 'lhwcn-member-data',
									'userId' : loginUuid
								});
							}

//		 				Club、Sterling & Aurelian
							switch(memberInfo.memberLevel) {
								case "Club":
									$("#levelC").show();
									$(".menu .pad-lf").css("border-color", "#00A9B7");
									break;
								case "Sterling":
									$("#levelS").show();
									$(".menu .pad-lf").css("border-color", "#838484");
									break;
								case "Aurelian":
									$("#levelA").show();
									$("#memberRoomUpgrad").hide();
									$(".menu .pad-lf").css("border-color", "#C29117");
									break;
							}

							closeQrcodeLoginWinw();
							showModalWinLogined();
							setCookies(email, $.md5(""), data);

							$("#member_logo").css({"display": "block"});
							$("#un_member_logo").css({"display": "none"});
							// 会员状态Active|Canceled|Expired|Suspended(Pending)
							if (memberInfo.renew == "Expired" || memberInfo.renew == "Canceled"|| memberInfo.renew == "Suspended") {
								renewRemind(memberInfo.renew, memberInfo.expirationdate);
								$.cookie(islogin_cookie, 2, {expires:date, path:'/', domain:'lhw.cn'});
							} else {
								$('#test-score').show();// searchbar.jsp

								if($(window).width() > 1500){
									$('.search .pub-w').css("width","1058px");
									$('.menu .pad-lf').css("width","1420px");
									$('.head-nav .pad-lf').css("width","1420px");
								}else{
									$('.search .pub-w').css("width","1058px");
									$('.menu .pad-lf').css("width","1200px");
									$('.head-nav .pad-lf').css("width","1200px");
								}

								var curruntPage = window.location.href;
								if (curruntPage.indexOf('/web/form') > 1) {
									memberOrderAutofills(data);
								}
								// 登录之后弹窗提示优惠活动
								seemore();
							}
							$('.detail_to_login').hide();
							let hotel_order_data = sessionStorage.getItem("hotel_order_data") || false;
							if(hotel_order_data){
								hotel_order_data_form(hotel_order_data);
							}else{
								if(window.pageLoad === true){
									location.reload()
								}
							}
						}else{
							closeQrcodeLoginWinw();
							showModalWinw();
						}

					} else {
						console.log("继续请求")
					}
				},
				error:function(jqXHR, textStatus, errorThrown){
					closeQrcodeLoginWinw();
					if(textStatus=="timeout"){
						clearInterval(interval);
						alert('网络连接超时，请检查网络后重试！');
					}else{
						clearInterval(interval);
						alert('失败，请检查网络后重试！');
					}
				}
			});
		}, 2000);

		setTimeout(function(){
			clearInterval(interval);
			closeQrcodeLoginWinw();
		},120000)
	}
	function qrcodeLoginReturn(uuidId){
		console.log(uuidId)
		$.post(
				"/qrlogin",
				{
					"uuidId" : uuidId
				},
				function(data) {
					if (data.isLogind == "1") {
//	 				window.location.assign("http://www.lhw.cn/index.html");
						var memberInfo = data.memberInfoResult;
						$("#score_is").text("积分：");
						$("#hello").text("你好,");
						$("#memberScore").text(scoreFmt(memberInfo.memberScore));
						$("#memberName").text(memberInfo.firstName).css('cursor','pointer').click(function(){
							location.href = '/member/info.html';
						});
						$("#roomUpgraded").text(memberInfo.roomUpgraded);
						$("#isLogind").val(data.isLogind);
						//设置登录追踪
						var dataLayer = window.dataLayer || [];
						var loginUuid = data.memberInfoResult.uuid
						console.log(loginUuid)
						if(loginUuid&&loginUuid!=""){
							dataLayer.push({
								'event' : 'lhwcn-member-data',
								'userId' : loginUuid
							});
						}

//	 				Club、Sterling & Aurelian
						switch(memberInfo.memberLevel) {
							case "Club":
								$("#levelC").show();
								$(".menu .pad-lf").css("border-color", "#00A9B7");
								break;
							case "Sterling":
								$("#levelS").show();
								$(".menu .pad-lf").css("border-color", "#838484");
								break;
							case "Aurelian":
								$("#memberRoomUpgrad").hide();
								$("#levelA").show();
								$(".menu .pad-lf").css("border-color", "#C29117");
								break;
						}
						showModalWinLogined();
						setCookies(email, $.md5(""), data);
						// 会员状态Active|Canceled|Expired|Suspended(Pending)
						if (memberInfo.renew == "Expired" || memberInfo.renew == "Canceled"|| memberInfo.renew == "Suspended") {
							renewRemind(memberInfo.renew, memberInfo.expirationdate);
							$.cookie(islogin_cookie, 2, {expires:date, path:'/', domain:'lhw.cn'});
						} else {
							$('#test-score').show();// searchbar.jsp

							if($(window).width() > 1500){
								$('.search .pub-w').css("width","1058px");
								$('.menu .pad-lf').css("width","1420px");
								$('.head-nav .pad-lf').css("width","1420px");
							}else{
								$('.search .pub-w').css("width","1058px");
								$('.menu .pad-lf').css("width","1200px");
								$('.head-nav .pad-lf').css("width","1200px");
							}

							var curruntPage = window.location.href;
							if (curruntPage.indexOf('/web/form') > 1) {
								memberOrderAutofills(data);
							}
							// 登录之后弹窗提示优惠活动
							seemore();
						}
						setTimeout(function(){
							FH.dispatch("${lhw:getAccountId('lhw.trace.memberLogin')}",$('body')[0]);
						},200);
					} else {
						if (data.memberInfoResult.code == 'FAIL') {
							showModalWinw();
						}
					}
				}).error(function(){
		});
		return true;

	}

	function closeQrcodeLoginWinw() {
		$(".qrcode-login-form").hide();
	}
	function closeModalWinw() {
		$(".weichat-form").hide();
	}

	function showModalWinp() {
		$(".forgot-form").show();
	}
	function closeModalWinp() {
		$(".forgot-form").hide();
	}
	function showModalWinPublicNum() {
		var islogind = $.cookie(islogin_cookie);
		if (islogind != '1') {
			var renew = $.cookie(renew_cookie);
			if (renew == 'Canceled' || renew == 'Expired'|| renew == "Suspended") {
				var expirationdate = $.cookie(expirationdate_cookie);
				renewRemind(renew, expirationdate);
			}
			return;
		}
		var uuid = $.cookie(uuid_cookie);
		$.post(
				"/promotion",
				{
					"uuid" : uuid
				},
				function(data) {
					if (data.isPromotion != 1) {
						$(".midder-button-content").hide();
						$(".midder-button-more").hide();
						$("#promotionTips").show();
						$("#noPromotionTips").hide();
						$("#noPromotionPub").show();

// 				$(".public-num-form").css("height", "190px");
					}else {
						$("#noPromotionTips").hide();
						$("#noPromotionPub").hide();
						$("#promotionTips").show();
						$("#public_promotion_desc").html(data.memberActivityResult.actDesc);
					}
					$(".promotion-form").hide();
					$(".promotion-box").hide();
					$(".page-mask").hide();
					$(".public-num-form").show();
				});
	}
	function closeModalWinPublicNum() {
		$(".public-num-form").hide();
	}
	function closeModalWinPromotion() {
		$(".promotion-form").hide();
		$(".promotion-box").hide();
		$(".page-mask").hide();
		$("body").removeClass("unscroll");
	}
	function showModalWinLogining() {
		$("#logining").show();
	}
	function closeModalWinLogining() {
		$("#logining").hide();
	}
	function showModalWinLogined() {
		$("#logined").show();
		$("#logining").hide();
		$(".head-logout").show();
	}
	function closeModalWinLogined() {
		$("#logined").hide();
		$("#logining").show();
	}
	function registry() {
		dataLayer.push({
			"event":"uaevent",
			"eventCategory":"Public",
			"eventAction":"Click_Sign-In",
			"eventLabel":"Sign-In Pop ups::Register",
		});
		// location.href = '/register.html';
		return;
		closeModalWin();
		showModalWinr();
	}
	function mregistry() {
		closeModalWin();
		closeModalWinr();
		closeModalWinw();
		closeModalWinp();
		showModalWinrm();
	}
	function registryw() {
		dataLayer.push({
			"event":"uaevent",
			"eventCategory":"Public",
			"eventAction":"Click_Sign-In",
			"eventLabel":"Sign-In Pop ups::Sign In With WeChat",
		});
		closeModalWin();

		showQrcodeLoginWinw();
// 	showModalWinw();
	}
	function forgotpwd() {
		closeModalWin();
		showModalWinp();
	}

	function toLogin() {
		closeModalWinr();
		showModalWin();
	}

	$(document).ready(function () {
		$(".head-logout").hide();
		$("#member_logo,#un_member_logo").click(function(){
			dataLayer.push({
				"event":"uaevent",
				"eventCategory":"Public",
				"eventAction":"Click_Top",
				"eventLabel":"Logo::LHW",
			});
		})
		$("#login").on("click", function () {
			$("#email-empty").hide();
			$("#password-empty").hide();
			closeModalWinr();
			closeModalWinrm();
			closeModalWinw();
			closeModalWinp();
			showModalWin();
			$(".login-form input[name='email']").focus();
			dataLayer.push({
				"event":"uaevent",
				"eventCategory":"Public",
				"eventAction":"Click_Top",
				"eventLabel":"Log In",
			});
			setTimeout(() => {
				dataLayer.push({
					"event":"uaevent",
					"eventCategory":"Public",
					"eventAction":"View_Sign-In",
					"eventLabel":"Sign-In Pop ups::View",
				});
			}, 500);

		});

		//判断是否是从重置密码页面跳转过来的，是的话弹出登录窗口
		var isOpenLoginButton = $.cookie("isOpenLoginButton");
		if(isOpenLoginButton==1){
			$.cookie("isOpenLoginButton", 0, {expires:-1, path:'/', domain:'lhw.cn'});
			$('#login').click();
		}

		$("#registry").on("click", function () {
			dataLayer.push({
				"event":"uaevent",
				"eventCategory":"Public",
				"eventAction":"Click_Top",
				"eventLabel":"Register",
			});
			location.href = "/register.html"
			return;
			closeModalWin();
			closeModalWinrm();
			closeModalWinw();
			closeModalWinp();
			showModalWinr();
		});
		$("#close_login").on("click", function () {
			closeModalWin();
			dataLayer.push({
				"event":"uaevent",
				"eventCategory":"Public",
				"eventAction":"Close_Sign-In",
				"eventLabel":"Sign-In Pop ups::Close",
			});
		});
		isLogin();
		checkLogin();

		$("#password").blur(function(){
			$("#email-error").hide();
			$("#password-error").hide();
		});
		$("#email").blur(function(){
			$("#email-error").hide();
			$("#password-error").hide();
		});

		var $errorValue = $('.login-form .value-error-msg span');
		$("#password").bind({
			copy : function(){
				$errorValue.text('禁止复制');
				setTimeout(function() {
					$errorValue.text('');
				}, 3000);
			},
			paste : function(){
				$errorValue.text('为保证密码安全，请勿粘贴');
				setTimeout(function() {
					$errorValue.text('');
				}, 3000);
			},
			cut : function(){
				$errorValue.text('禁止剪切');
				setTimeout(function() {
					$errorValue.text('');
				}, 3000);
			}
		});

		$("#rome-upgraded").on({
			"mouseenter":function(){
				$(this).find(".rome-upgraded-float-layer").show();
			},
			"mouseleave":function(){
				$(this).find(".rome-upgraded-float-layer").hide();
			}
		});

		$('.login-form .form input[name=email]').blur(function () {
			var emailReg =  /\w+((-w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+/;
			var email = $(this).val().trim();
			var $email = $('.form').find('input[name=email]');
			var $errorName = $('.name-error-msg span');
			if (!email) {
				var $errorValue = $('.value-error-msg span');
				clearContent($errorValue, 100);
				$(".login-form .value-warn").removeClass("visible");
				$errorName.text('邮箱不能为空！');
				$email.addClass('invalidInput');
				$(".login-form .name-warn").addClass("visible");
				return false;
			}
			if (!emailReg.test(email)) {
				var $errorValue = $('.value-error-msg span');
				clearContent($errorValue, 100);
				$(".login-form .value-warn").removeClass("visible");
				$errorName.text('邮箱格式错误，请重新输入！');
				$email.addClass('invalidInput');
				$(".login-form .name-warn").addClass("visible");
				return false;
			}
			clearContent($errorName, 100);
			$(".login-form .name-warn").removeClass("visible");
		});

		$('.login-form .form input[name=password]').blur(function () {
			var value = $(this).val().trim();
			var $value = $('.form').find('input[name=value]');
			var $errorValue = $('.value-error-msg span');
			if (!value) {
				var $errorName = $('.name-error-msg span');
				clearContent($errorName, 100);
				$(".login-form .name-warn").removeClass("visible");
				$errorValue.text('密码不能为空！');
				$value.addClass('invalidInput');
				$(".login-form .value-warn").addClass("visible");
				return false
			}
			clearContent($errorValue, 100);
			$(".login-form .value-warn").removeClass("visible");
		});
	});

	function clearCookies() {
		$.cookie(islogin_cookie, 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie(identify_cookie, 1, {expires:-1, path:'/', domain:'lhw.cn'});
//   	$.cookie(key_code_cookie, passHash, {expires:date, path:'/', domain:'lhw.cn'});
		$.cookie(member_level_cookie, 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie(member_score_cookie, 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie(member_lcnumber_cookie, 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie(first_name_cookie, 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie(last_name_cookie, 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie(prefix_cookie, 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie(phone_cookie, 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie(uuid_cookie, 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie(roomUpgraded_cookie, 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie(isopen_cookie, 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie(renew_cookie, 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie(expirationdate_cookie, 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie('ot', 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie('guestPhone', 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie('guestEmail', 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie('guestLastNameCN', 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie('guestLastName', 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie('guestFirstNameCN', 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie('guestFirstName', 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie('memberNo', 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie('countryCode', 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie('remarks', 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie('ot', 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie('title', 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie('isFirstPage', 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie('searchCondition', 1, {expires:-1, path:'/', domain:'lhw.cn'});
		$.cookie('source', 1, {expires:-1, path:'/', domain:'lhw.cn'});
	}

	$('.logout-btn').click(function(){
		$(".page-mask").show();
		$("body").addClass("unscroll");
		var text = "确定要退出吗？";
		common.confirm(text , function(param) {
			if (param == "1") {
				clearCookies();
				$(".head-logout").hide();
				location.reload();
			}
			$(".page-mask").hide();
			$("body").removeClass("unscroll");
		});

	});

	function noMemberbook(class1) {
		class1 = "." + class1
		$(class1).hide();
	}

	function showPartaking() {
		$(".midder-button-partaking").show();
		$(".midder-button-isee").hide();
		$(".midder-button-partaked").hide();
		$(".promotion-form").css("height","460");
	}

	function showPartaked() {
		$(".midder-button-partaked").show();
		$(".midder-button-partaking").hide();
		$(".midder-button-isee").hide();
		$(".promotion-form").css("height","540");
	}

	function showIsee() {
		$(".midder-button-isee").show();
		$(".midder-button-partaked").hide();
		$(".midder-button-partaking").hide();
		$(".promotion-form").css("height","440");
	}
	function showMask() {
		$('.loading-mask').show();
	}
	function hideMask() {
		$('.loading-mask').hide();
	}
	// function clearContent($context) {
	//     setTimeout(function() {
	//         $context.text('');
	//         $(".form input").removeClass("invalidInput");
	//         $(".login-form .name-warn").removeClass("visible");
	//         $(".login-form .value-warn").removeClass("visible");
	//     }, 3000);
	// }

	function clearContent($context, time) {
		setTimeout(function() {
			$context.text('');
			$(".form input").removeClass("invalidInput");
		}, time || 3000);
	}

	function clearContent1($context) {
//     setTimeout(function() {
		$context.text('');
		$(".form input").removeClass("invalidInput");
		$(".login-form .name-warn").removeClass("visible");
		$(".login-form .value-warn").removeClass("visible");
//     }, 3000);
	}

	function memberPwdOnpaste() {
		var $errorValue = $('.login-form .value-error-msg span');
		$errorValue.text('为保证密码安全，请勿粘贴');
		setTimeout(function() {
			$errorValue.text('');
// 		$(".form input").removeClass("invalidInput");
		}, 3000);
		return true;
	}
	function memberPwdOnTextmenu() {
		return true;
	}
	function memberPwdOncopy() {
		var $errorValue = $('.login-form .value-error-msg span');
		$errorValue.text('禁止复制');
		setTimeout(function() {
			$errorValue.text('');
		}, 3000);
		return true;
	}
	function memberPwdOncut() {
		var $errorValue = $('.login-form .value-error-msg span');
		$errorValue.text('禁止剪切');
		setTimeout(function() {
			$errorValue.text('');
		}, 3000);
		return true;
	}

	// 查看更多现实活动信息
	function seemore() {
		var uuid = $.cookie(uuid_cookie);
		$.post(
				"/promotion",
				{
					"uuid" : uuid
				},
				function(data) {
					if (data.isPromotion == 1) {
						closeModalWinPublicNum();
						showModalWinPromotion(data);
					}
				});
	}
	function showModalWinPromotion(data) {
		$("#promotionImg").attr('src', data.memberActivityResult.imgSrc);
		$("#actname").html(data.memberActivityResult.actname);
		$("#actDesc").html(data.memberActivityResult.actDesc);
		$("#actId").val(data.memberActivityResult.actId);

		var isPartake = data.memberActivityResult.isPartake;
		if (isPartake == '0') {
			showPartaking();
			$(".promotionTip").show();
		} else if (isPartake == '1') {
			showPartaked();
		} else if (isPartake == '2') {
			showIsee();
		}
		closeModalWinPublicNum();
		$(window).scrollTop(0);
		$(".page-mask").show();
		$("body").addClass("unscroll");
		$(".promotion-form").show();
		$(".promotion-box").show();
	}

	function partakingActivity() {
		var actId = $("#actId").val();
		var uuid = $.cookie(uuid_cookie);
		$.post(
				"/partakeActivity",
				{
					"uuid" : uuid,
					"actId" : actId
				},
				function(data) {
					if (data.code == "SUCCESS") {
						showPartaked();
						$(".promotionTip").hide();
						$(".promotion-form").css("height","485");
					}
				});
	}

	function partakedActivity() {
		closeModalWinPromotion();
	}

	function iseeActivity() {
		closeModalWinPromotion();
	}

	function isLogin() {
		var isLogind = $.cookie(islogin_cookie);
		console.log(isLogind)
		if (isLogind == '1' || isLogind == '2') {
			closeModalWinLogining();
			showModalWinLogined();
			$("#member_logo").css({"display": "block"});
			$("#un_member_logo").css({"display": "none"});
		} else {
			showModalWinLogining();
			$("#score_is").text("");
			$("#hello").text("");
			$("#member_logo").css({"display": "none"});
			$("#un_member_logo").css({"display": "block"});
		}
	}


	function setCookies(email, passHash, data) {
		var memberInfo = data.memberInfoResult;
		$.cookie(islogin_cookie, data.isLogind, {expires:date, path:'/', domain:'lhw.cn'});
		$.cookie(identify_cookie, data.username, {expires:date, path:'/', domain:'lhw.cn'});
		$.cookie(member_level_cookie, memberInfo.memberLevel, {expires:date, path:'/', domain:'lhw.cn'});
		$.cookie(member_score_cookie, memberInfo.memberScore, {expires:date, path:'/', domain:'lhw.cn'});
		$.cookie(member_lcnumber_cookie, memberInfo.memberNo, {expires:date, path:'/', domain:'lhw.cn'});
		$.cookie(first_name_cookie, memberInfo.firstName, {expires:date, path:'/', domain:'lhw.cn'});
		$.cookie(last_name_cookie, memberInfo.lastName, {expires:date, path:'/', domain:'lhw.cn'});
		$.cookie(prefix_cookie, memberInfo.prefix, {expires:date, path:'/', domain:'lhw.cn'});
		$.cookie(phone_cookie, memberInfo.phoneNumber, {expires:date, path:'/', domain:'lhw.cn'});
		$.cookie(uuid_cookie, memberInfo.uuid, {expires:date, path:'/', domain:'lhw.cn'});
		$.cookie(roomUpgraded_cookie, memberInfo.roomUpgraded, {expires:date, path:'/', domain:'lhw.cn'});
		$.cookie(renew_cookie, memberInfo.renew, {expires:date, path:'/', domain:'lhw.cn'});
		$.cookie(expirationdate_cookie, memberInfo.expirationdate, {expires:date, path:'/', domain:'lhw.cn'});
	}

	function checkLoginBycookie() {
		var islogind = $.cookie(islogin_cookie);
		var memberLevel = $.cookie(member_level_cookie);
		var memberScore = $.cookie(member_score_cookie);
		var firstName = $.cookie(first_name_cookie);
		var roomUpgraded = $.cookie(roomUpgraded_cookie);
		console.log(memberLevel + ", " + memberScore + ", " + firstName);
		if ((islogind=='1'||islogind=='2')&&typeof(memberLevel) != 'undefined'&&typeof(memberScore != 'undefined')&&typeof(firstName != 'undefined')) {
			$("#score_is").text("积分：");
			$("#hello").text("你好,");
			$("#memberScore").text(scoreFmt(memberScore));
			$("#memberName").text(firstName).css('cursor','pointer').click(function(){
				location.href = '/member/info.html';
			});
			$("#roomUpgraded").text(roomUpgraded);
// 		alert(memberLevel);
			switch(memberLevel) {
				case "Club":
					$("#levelC").show();
					$(".menu .pad-lf").css("border-color", "#00A9B7");
					break;
				case "Sterling":
					$("#levelS").show();
					$(".menu .pad-lf").css("border-color", "#838484");
					break;
				case "Aurelian":
					$("#levelA").show();
					$(".menu .pad-lf").css("border-color", "#C29117");
					$("#memberRoomUpgrad").hide();
					break;
			}
			showModalWinLogined();
		} else {
			showModalWinLogining();
		}
	}

	function checkLogin() {
		checkLoginBycookie();
		var islogin = $.cookie(islogin_cookie);
		if(islogin != "" && typeof(islogin) != 'undefined') {
			var uuid = $.cookie(uuid_cookie);
			$.post(
					"/checkLogin",
					{
						"uuid" : uuid
					},
					function(data) {
						if (data.isLogind == "1") {
							var memberInfo = data.memberInfoResult;
							$("#score_is").text("积分：");
							$("#hello").text("你好,");
							$("#memberScore").text(scoreFmt(memberInfo.memberScore));
							$("#memberName").text(memberInfo.firstName).css('cursor','pointer').click(function(){
								location.href = '/member/info.html';
							});
							$("#roomUpgraded").text(memberInfo.roomUpgraded);
							// Club、Sterling & Aurelian
							switch(memberInfo.memberLevel) {
								case "Club":
									$("#levelC").show();
									$(".menu .pad-lf").css("border-color", "#00A9B7");
									break;
								case "Sterling":
									$("#levelS").show();
									$(".menu .pad-lf").css("border-color", "#838484");
									break;
								case "Aurelian":
									$("#levelA").show();
									$(".menu .pad-lf").css("border-color", "#C29117");
									$("#memberRoomUpgrad").hide();
									break;
							}
							// 测试
// 					if (memberInfo.email == '<EMAIL>') {
// 	              		memberInfo.renew = "Canceled";//Canceled
// 					}
							showModalWinLogined();
							closeModalWinLogining();
							setCookies(email, "", data);
							if (memberInfo.renew == "Expired" || memberInfo.renew == "Canceled"|| memberInfo.renew == "Suspended") {
								$.cookie(islogin_cookie, 2, {expires:date, path:'/', domain:'lhw.cn'});
							} else {
								var curruntPage = window.location.href;
								if (curruntPage.indexOf('/web/form') > 1) {
									memberOrderAutofills(data);
								}
							}
						} else {
							showModalWinLogining();
							closeModalWinLogined();
						}
					});
		}
	}

	/* 获取指定cookie */
	function getCookie(name) {
		var strCookie = document.cookie;
		var arrCookie = strCookie.split("; ");
		for (var i = 0; i < arrCookie.length; i++) {
			var arr = arrCookie[i].split("=");
			if (arr[0] == name)
				return arr[1];
		}
		return "";
	}

	function clearInput() {
		$(".login-form input[name='email']").val("");
		$(".login-form input[name='value']").val("");
	}

	$('.login-form').bind('keydown', function(event) {
		var $btn =  $(this).find('.btn-submit button');
		if (event.keyCode == "13") {
			//回车执行查询
			if (!$btn.attr("disabled")) {
				$btn.trigger("click");
			}
		}
	});


	// 验证输入并触发验证码
	function memberLogin() {
		var $email = $('.form').find('input[name=email]');
		var $value = $('.form').find('input[name=value]');
		var email = $("#email").val().trim();
		var password = $("#password").val().trim();
		var emailReg =  /\w+((-w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+/;
		var $errorName = $('.login-form .name-error-msg span');
		var $errorValue = $('.login-form .value-error-msg span');

		// 验证邮箱
		if (!email) {
			$errorName.text('邮箱不能为空！');
			$email.addClass('invalidInput');
			$(".login-form .name-warn").addClass("visible");
			$("#email").focus();
			return false;
		}

		if (!emailReg.test(email)) {
			$errorName.text('邮箱格式错误，请重新输入！');
			$email.addClass('invalidInput');
			$(".login-form .name-warn").addClass("visible");
			$("#email").focus();
			return false;
		}

		// 验证密码
		if (!password) {
			$errorValue.text('密码不能为空！');
			$value.addClass('invalidInput');
			$(".login-form .value-warn").addClass("visible");
			$("#password").focus();
			return false
		}

		// 清除错误提示
		clearContent($errorName, 100);
		clearContent($errorValue, 100);
		$(".login-form .value-warn").removeClass("visible");
		$(".login-form .name-warn").removeClass("visible");

		// 输入验证通过，验证码会自动弹出
		// performLogin() 将在验证码成功后被调用
		return true;
	}

	// 实际执行登录的函数
	function performLogin() {
		var email = $("#email").val().trim();
		var password = $("#password").val().trim();

		dataLayer.push({
			"event":"uaevent",
			"eventCategory":"Public",
			"eventAction":"Click_Sign-In",
			"eventLabel":"Sign-In Pop ups::Sign In",
		});
		showMask();
		$(".btn-submit button").attr("disabled", "disabled");
		let hotel_order_data = sessionStorage.getItem("hotel_order_data") || false;
		var ajaxTimeoutTest = $.ajax({
			url:'/mlogin',  //请求的URL
			timeout : 60000, //超时时间设置，单位毫秒
			type : 'post',  //请求方式，get或post
			data :{
				"email" : email,
				"password" : password,
				"captchaVerifyParam" : window.captchaVerifyParam || '',
				"sceneId" : window.captchaSceneId || ''
			},  //请求所传参数，json格式
			dataType:'json',//返回的数据格式
			success:function(data){ //请求成功的回调函数
				var memberInfo = data.memberInfoResult;
				var valid = true;
				if (memberInfo.renew == "Expired" || memberInfo.renew == "Canceled"|| memberInfo.renew == "Suspended") {
					valid = false;
				}
				if (data.isLogind == "1" && valid) {
// 				window.location.assign("http://www.lhw.cn/index.html");

					$("#score_is").text("积分：");
					$("#hello").text("你好,");
					$("#memberScore").text(scoreFmt(memberInfo.memberScore));
					$("#memberName").text(memberInfo.firstName).css('cursor','pointer').click(function(){
						location.href = '/member/info.html';
					});
					$("#roomUpgraded").text(memberInfo.roomUpgraded);
					$("#isLogind").val(data.isLogind);
					//设置登录追踪
					var dataLayer = window.dataLayer || [];
					var loginUuid = data.memberInfoResult.uuid
					console.log(loginUuid)
					if(loginUuid&&loginUuid!=""){
						dataLayer.push({
							'event' : 'lhwcn-member-data',
							'userId' : loginUuid
						});
					}
// 				Club、Sterling & Aurelian
					switch(memberInfo.memberLevel) {
						case "Club":
							$("#levelC").show();
							$(".menu .pad-lf").css("border-color", "#00A9B7");
							break;
						case "Sterling":
							$("#levelS").show();
							$(".menu .pad-lf").css("border-color", "#838484");
							break;
						case "Aurelian":
							$("#levelA").show();
							$(".menu .pad-lf").css("border-color", "#C29117");
							$("#memberRoomUpgrad").hide();
							break;
					}
// 				// 测试
// 				if (email == '<EMAIL>') {
//               		memberInfo.renew = "Canceled";// Canceled
// 				}
					closeModalWin();
					hideMask();

					// 清除验证码参数，准备下次登录
					window.captchaVerifyParam = '';
					window.captchaSceneId = '';

					$("#member_logo").css({"display": "block"});
					$("#un_member_logo").css({"display": "none"});

					$(".btn-submit button").removeAttr("disabled", "disabled")
					showModalWinLogined();
					setCookies(email, $.md5(password), data);
					// 会员状态Active|Canceled|Expired|Suspended(Pending)

					$('#test-score').show();// searchbar.jsp

					if($(window).width() > 1500){
						$('.search .pub-w').css("width","1058px");
						$('.menu .pad-lf').css("width","1420px");
						$('.head-nav .pad-lf').css("width","1420px");
					}else{
						$('.search .pub-w').css("width","1058px");
						$('.menu .pad-lf').css("width","1200px");
						$('.head-nav .pad-lf').css("width","1200px");
					}

					var curruntPage = window.location.href;
					if (curruntPage.indexOf('/web/form') > 1) {
						memberOrderAutofills(data);
					}
					$('.detail_to_login').hide();
					// 登录之后弹窗提示优惠活动
					seemore();
					if(hotel_order_data){
						hotel_order_data_form(hotel_order_data);
					}else{
						if(window.pageLoad === true){
							location.reload()
						}
					}
					setTimeout(function(){
						FH.dispatch("${lhw:getAccountId('lhw.trace.memberLogin')}",$('body')[0]);
					},200);
				} else {
					// 登录失败，清除验证码参数
					window.captchaVerifyParam = '';
					window.captchaSceneId = '';

					if (memberInfo.renew == "Expired" || memberInfo.renew == "Canceled"|| memberInfo.renew == "Suspended") {
						renewRemind(memberInfo.renew, memberInfo.expirationdate);
						hideMask();
						$(".btn-submit button").removeAttr("disabled");
						closeModalWin();
					} else if (data.memberInfoResult.code == 'FAIL') {
						$errorName.text('邮箱或密码错误');
						$email.addClass('invalidInput');
						$(".login-form .name-warn").addClass("visible");
						$errorValue.text('邮箱或密码错误');
						$value.addClass('invalidInput');
						$(".login-form .value-warn").addClass("visible");
						$btn.removeAttr("disabled");
						hideMask();
						$(".btn-submit button").removeAttr("disabled", "disabled");
					}
				}
			},
			complete : function(XMLHttpRequest,status){ //请求完成后最终执行参数
				if(status=='timeout'){//超时,status还有success,error等值的情况
					ajaxTimeoutTest.abort();
					// 超时时清除验证码参数
					window.captchaVerifyParam = '';
					window.captchaSceneId = '';
					$errorName.text('登录超时，请重试');
					$email.addClass('invalidInput');
					$(".login-form .name-warn").addClass("visible");
					$(".login-form .value-warn").addClass("visible");
					$btn.removeAttr("disabled");
					hideMask();
					$(".btn-submit button").removeAttr("disabled", "disabled");
				}
			},
			error:function(){
			}
		});
		return true;
	}

	function renewRemind(status, date) {
		// 会员状态Active|Canceled|Expired|Suspended(Pending)
		if (status == 'Canceled'|| status == "Suspended") {
			$('#cancelled').show();
			$('#expired').hide();
			$('.renew-form .footer').hide();
		} else if (status == 'Expired') {
			$('#expirationdate').text(date);
			$('#cancelled').hide();
			$('#expired').show();
			$('.renew-form .footer').show();
		}
		$(".renew-form").show();
		$(".page-mask").show();
		clearCookies();
	}

	function closeRenewWinw() {
		$(".renew-form").hide();
		$(".page-mask").hide();
		$("body").removeClass("unscroll");
	}

	function scoreFmt(score) {
		if (typeof score == "undefined" || score == null || score == "") {
			score = '0';
		}
		var length = score.length;
		if(length>3){
			score1 = score.substr(0,length-3);
			score2 = score.substring(length-3,length);
			score = score1+","+score2;
		}
		return score;
	}
	$(".promotion-box").height($(window).height());

</script>
