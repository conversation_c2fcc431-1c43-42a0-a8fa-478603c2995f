<#include "/common/common.ftl" />
<!DOCTYPE html>
<html>
<@head.head basePath="//${basePath}" title="尊享贵宾会会员登录 _立鼎世酒店集团官方网站_lhw.cn" 
	description="立鼎世博揽豪华独立酒店精粹，遍布全球75多个国家的375余家成员酒店独具特色的细节与风格，完美诠释各目的地的文化特色，服务于热衷探索文化的旅行者。- lhw.cn" 
	keywords="奢华酒店,国际奢华酒店查询预订,国外奢华酒店查询预订，豪华酒店，国际豪华酒店查询预订，国外豪华酒店查询预订">
<script type="text/javascript">

</script>
<@gtm />
</@head.head>
<body style="background:#f4f4f4;">


<div id="wrapper">

	<@header.header basePath="//${basePath}" loginHeader="login-header"/>
	<@floatBanner.floatBanner basePath="//${basePath}" />
    <div class="mainbody">
		<div class="login-page-wrapper">
		    <section class="main">
		        <div class="title text-center"><span>会员登录</span></div>
		        <div class="workspace text-center">
		            <div class="form">
		                <div class="name">
		                    <label><img alt="img" src="/static/img/email2x.png"/></label>
		                    <input name="email" maxlength="32" type="email" value="" placeholder="注册会籍时使用的邮箱">
		                </div>
		                <div class="name-error-msg"><span></span><img class="name-error-img" src="/static/img/warn_1.png"/></div>
		                <div class="value">
		                    <label><img alt="img" src="/static/img/pwd2x.png"/></label>
		                    <input name="value" type="password" value="" oncopy="return false;" onpaste="return false;" 
							oncut="return false;" oncontextmenu="return false;" autocomplete="new-password" placeholder="区分大小写">
		                </div>
		                <div class="value-error-msg"><span></span><img class="value-error-img" src="/static/img/warn_1.png"/></div>
		            </div>
		            <div class="options">
		                <a class="float-left" href="javascript: void(0);">忘记密码</a>
		<!--                 <a class="float-right" href="javascript: registry();">注册</a> -->
		                <a onclick='dataLayer.push({
							"event": "uaevent",
							"eventCategory": "Login",
							"eventAction": "Click_Button",
							"eventLabel": "Register"
						})' class="float-right" href="/register.html">注册</a>
		                <div class="clear-float"></div>
		            </div>
		            <div class="login-btn" id="mlogin">
		                <a class="submit" href="javascript:void(0)">登录</a>
		            </div>
		            <div class="weixin-login" id="weixin-login-id">
						<img src="/static/img/wechat_1.png"/>
						<a class="a-login" href="#">微信登录</a>
					</div>
		        </div>
		    </section>
		</div>
	</div>
<!--	<div class="footer login-footer">
    	<a href="//m.lhw.cn" class="logo"></a>
        <p class="pt15">使用本站表明您已接受<a href="//${basePath}/privacy-policy">隐私条款</a>和<a href="http://m.lhw.cn/terms-and-conditions">条款及细则</a><br>©2017 The Leading Hotels of the World, Ltd.</p>
        <p class="pt15">预订热线</p>
        <p class="pt10"><a href="tel://4001324582"><img src="//${basePath}/static/img/optimization/ico-tel-num.png" width="120px"></a></p>
    </div> -->
    <div class="footer" style="margin:0; padding:3em 0 4em;">
    	<a href="//m.lhw.cn" class="logo"></a>
        <p class="pt15" style="font-size:0.75rem; color:#fff;">预订热线：</p>
        <p class="" style="font-size: 1.35rem; margin-bottom:40px;"><a href="tel://4001324582" style="text-decoration:none;">400-1324-582</a></p>      	
        <p style="color: #4c83ad;">使用本站表明您已接受<a href="//${basePath}/privacy-policy" style="color: #4c83ad;">隐私条款</a>和<a href="http://m.lhw.cn/terms-and-conditions" style="color: #4c83ad;">条款及细则</a></p>
        <p>©2019 The Leading Hotels of the World, Ltd.</p>
        <p>ICP证: 沪15005237号-1</p>
        <p>中国上海市南京西路1376号，上海商城东峰342室</p>
    </div>    
</div>
		
<div class="loading-mask">
    <img src="/static/img/small_loader.gif" alt="gif"/>
</div>

<@script.script basePath="//${basePath}">
<script type="text/javascript">

$(document).ready(function(){
	$("#weixin-login-id").css({"display": "none"});
	//if(isWeiXin()){
	//	console.log("shi")
	//	var code = getUrlParam("code");
	//	if(code){
	//		wxLogin();
	//	}
	//}else{
	//	$("#weixin-login-id").css({"display": "none"});
	//}
	
})

function isWeiXin(){
  //window.navigator.userAgent属性包含了浏览器类型、版本、操作系统类型、浏览器引擎类型等信息，这个属性可以用来判断浏览器类型
  var ua = window.navigator.userAgent.toLowerCase();
  //通过正则表达式匹配ua中是否含有MicroMessenger字符串
  if(ua.match(/MicroMessenger/i) == 'micromessenger'){
  return true;
  }else{
  return false;
  }
}

$('.weixin-login').on('click', function() {
	wxLogin();
});

function getUrlParam(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]);
    return null;
}

function wxLogin(callback) {
	//appid=wx5177b210a12f1dff
	//http://member.lhw.cn/lhw-member
    var appId = 'wx5ec0c2be82d29fc3';
    var oauth_url = '//member.lhw.cn/lhw-member/qrcode/mlogin';
    var url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" + appId + "&redirect_uri=" + location.href.split('#')[0] 
    	+ "&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect";
	var code = getUrlParam("code");
    if (!code) {
        window.location = url;
    } else {
    	$.ajax({
            async:true,
            cache:false,
            timeout:60000,
            type:"GET",
            url:oauth_url,
            data:{
            	code: code
			},
            error:function(jqXHR, textStatus, errorThrown){
              if(textStatus=="timeout"){
            	  alert("加载超时！请重试！");
              }else{
            	  alert('登录异常！请重试！');
              }
            },
            success:function(data) {
				if (data.code == "SUCCESS") {
				setTimeout(function(){
					FH.dispatch("${lhwTraceMemberLogin}",$('body')[0]);
				},200);
					var memberInfo = data;
					$("#score_is").text("积分：");
			 		$("#hello").text("你好");
					$("#memberScore").text(toThousands(memberInfo.memberScore));
					$("#memberName").text(memberInfo.firstName);
					$("#roomUpgraded").text(memberInfo.roomUpgraded);
					$("#isLogind").val("1");
					//$("#isLogind").val(data.isLogind);
					// Club、Sterling & Aurelian
					switch(memberInfo.memberLevel) {
						case "Club":
							$("#levelC").show();
						  break;
						case "Sterling":
							$("#levelS").show();
						  break;
						case "Aurelian":
							$("#levelA").show();
							$("#memberRoomUpgrad").hide();
						  break;
					}
					$("#member_logo").css({"display": "block"});
			    	$("#un_member_logo").css({"display": "none"});
					
					var data2 = {};
					data2['username'] = memberInfo.email;
					data2['isLogind'] = '1';
					data2['memberInfoResult'] = memberInfo;
					setCookies(memberInfo.email, '', data2);
					$.cookie(justLogind_cookie, 1, {expires:date, path:'/', domain:'lhw.cn'});
					// 会员状态Active|Canceled|Expired|Suspended(Pending)
	           		if (memberInfo.renew == "Expired" || memberInfo.renew == "Canceled"|| memberInfo.renew == "Suspended") {
						$.cookie(islogin_cookie, 2, {expires:date, path:'/', domain:'lhw.cn'});
						$.cookie(expirationdate_cookie, memberInfo.expirationdate, {expires:date, path:'/', domain:'lhw.cn'});
	           		}
					window.location.assign("/");
				}
			}
        });
    }
}
</script>
</@script.script>
</body>
</html>